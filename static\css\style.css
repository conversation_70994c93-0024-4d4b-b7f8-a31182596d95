/* 跨境电商测试用例管理系统 - 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 渐变背景 */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 卡片悬停效果 */
.hover-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    border-color: #007bff;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* Jumbotron样式 */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.jumbotron h1 {
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.jumbotron .lead {
    font-size: 1.1rem;
    opacity: 0.95;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

.card-title {
    font-weight: 600;
    color: #2c3e50;
}

.card-text {
    line-height: 1.6;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
}

/* 场景列表样式 */
.scenario-list {
    max-height: 200px;
    overflow-y: auto;
}

.scenario-list::-webkit-scrollbar {
    width: 4px;
}

.scenario-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.scenario-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.scenario-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-weight: bold;
    color: #6c757d;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.modal-title {
    font-weight: 600;
    color: #2c3e50;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* 加载动画 */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* 文件列表样式 */
.file-item {
    transition: background-color 0.3s ease;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.file-item:hover {
    background-color: #f8f9fa;
}

.file-size {
    font-size: 0.875rem;
    color: #6c757d;
}

.file-date {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 国家标签 */
.country-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    background-color: #007bff;
    color: white;
    border-radius: 12px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.country-badge:hover {
    background-color: #0056b3;
    transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .scenario-list {
        max-height: 150px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #2c3e50;
    border-radius: 4px;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    background-color: #007bff;
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-info {
    border-left-color: #17a2b8;
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-success {
    border-left-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    border-left-color: #ffc107;
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}
