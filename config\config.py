"""
配置文件 - ERNIE Bot SDK 配置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # ERNIE Bot 配置
    ERNIE_ACCESS_TOKEN = os.getenv('ERNIE_ACCESS_TOKEN')
    ERNIE_API_TYPE = os.getenv('ERNIE_API_TYPE', 'aistudio')
    ERNIE_MODEL = os.getenv('ERNIE_MODEL', 'ernie-3.5')
    
    # 输出配置
    OUTPUT_DIR = 'test_cases'
    KNOWLEDGE_BASE_DIR = 'knowledge_base'
    REQUIREMENTS_DIR = 'requirements'
    SCRIPTS_DIR = 'scripts'
    
    @classmethod
    def validate(cls):
        """验证配置"""
        if not cls.ERNIE_ACCESS_TOKEN:
            raise ValueError("请在.env文件中设置ERNIE_ACCESS_TOKEN")
        return True
