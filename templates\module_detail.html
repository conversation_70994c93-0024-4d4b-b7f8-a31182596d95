{% extends "base.html" %}

{% block title %}{{ path_parts[-1] }} - 模块详情{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{{ url_for('index') }}">
                <i class="fas fa-home me-1"></i>首页
            </a>
        </li>
        {% for i in range(path_parts|length) %}
            {% if loop.last %}
                <li class="breadcrumb-item active" aria-current="page">
                    {{ path_parts[i] }}
                </li>
            {% else %}
                <li class="breadcrumb-item">
                    <a href="{{ url_for('module_detail', module_path='/'.join(path_parts[:i+1])) }}">
                        {{ path_parts[i] }}
                    </a>
                </li>
            {% endif %}
        {% endfor %}
    </ol>
</nav>

<!-- 模块标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h1 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    {{ path_parts[-1] }}
                </h1>
                <p class="card-text mt-2 mb-0">
                    {% if scenarios %}
                        包含 {{ scenarios|length }} 个{% if scenarios.values()|selectattr('测试场景')|list %}子模块{% else %}测试场景{% endif %}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 场景/子模块列表 -->
<div class="row">
    {% for scenario_name, scenario_info in scenarios.items() %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <span class="me-2" style="font-size: 1.1em;">
                        {{ scenario_info.get('icon', '📄') }}
                    </span>
                    {{ scenario_name }}
                </h5>
                {% if scenario_info.get('优先级') %}
                <span class="badge {% if scenario_info['优先级'] == '高' %}bg-danger{% elif scenario_info['优先级'] == '中' %}bg-warning{% else %}bg-secondary{% endif %}">
                    {{ scenario_info['优先级'] }}
                </span>
                {% endif %}
            </div>
            
            <div class="card-body">
                <p class="card-text text-muted">
                    {{ scenario_info.get('description', '暂无描述') }}
                </p>
                
                <!-- 如果有测试场景，显示场景列表 -->
                {% if scenario_info.get('测试场景') %}
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-list-ul me-1"></i>
                        测试场景 ({{ scenario_info['测试场景']|length }})
                    </h6>
                    <div class="scenario-list">
                        {% for test_scenario, test_info in scenario_info['测试场景'].items() %}
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                            <span class="small">{{ test_scenario }}</span>
                            <span class="badge {% if test_info.get('优先级') == '高' %}bg-danger{% elif test_info.get('优先级') == '中' %}bg-warning{% else %}bg-secondary{% endif %} small">
                                {{ test_info.get('优先级', '中') }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- 如果有测试点，显示测试点 -->
                {% if scenario_info.get('测试点') %}
                <div class="mb-3">
                    <h6 class="text-success">
                        <i class="fas fa-check-circle me-1"></i>
                        测试点
                    </h6>
                    <div class="d-flex flex-wrap">
                        {% for test_point in scenario_info['测试点'] %}
                        <span class="badge bg-light text-dark me-1 mb-1 small">
                            {{ test_point }}
                        </span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer bg-transparent">
                {% if scenario_info.get('测试场景') %}
                    <!-- 如果有子场景，进入下一层 -->
                    <a href="{{ url_for('module_detail', module_path=module_path + '/' + scenario_name) }}" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>
                        查看场景
                    </a>
                {% else %}
                    <!-- 如果是最终场景，查看测试用例 -->
                    <a href="{{ url_for('scenario_detail', scenario_name=scenario_name) }}" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-list-alt me-1"></i>
                        查看用例
                    </a>
                    <button class="btn btn-outline-primary btn-sm ms-2" 
                            onclick="generateForScenario('{{ scenario_name }}')">
                        <i class="fas fa-magic me-1"></i>
                        AI生成
                    </button>
                {% endif %}
                
                <small class="text-muted ms-2">
                    {% if scenario_info.get('测试场景') %}
                    {{ scenario_info['测试场景']|length }} 个场景
                    {% elif scenario_info.get('测试点') %}
                    {{ scenario_info['测试点']|length }} 个测试点
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 如果没有场景 -->
{% if not scenarios %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle me-2"></i>
            该模块暂无测试场景，请联系管理员添加。
        </div>
    </div>
</div>
{% endif %}

<!-- 操作按钮 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-tools me-2"></i>
                    批量操作
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="generateAllScenarios()">
                        <i class="fas fa-magic me-1"></i>
                        批量生成用例
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="exportModule()">
                        <i class="fas fa-download me-1"></i>
                        导出模块用例
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showModuleStats()">
                        <i class="fas fa-chart-bar me-1"></i>
                        模块统计
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块统计模态框 -->
<div class="modal fade" id="moduleStatsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    {{ path_parts[-1] }} 模块统计
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ scenarios|length }}</h4>
                            <small>场景数量</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-success">
                                {% set high_priority = scenarios.values()|selectattr('优先级', 'equalto', '高')|list|length %}
                                {{ high_priority }}
                            </h4>
                            <small>高优先级</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">
                                {% set total_test_points = 0 %}
                                {% for scenario in scenarios.values() %}
                                    {% if scenario.get('测试点') %}
                                        {% set total_test_points = total_test_points + scenario['测试点']|length %}
                                    {% endif %}
                                {% endfor %}
                                {{ total_test_points }}
                            </h4>
                            <small>测试点</small>
                        </div>
                    </div>
                </div>
                
                <h6>优先级分布</h6>
                {% set priority_counts = {'高': 0, '中': 0, '低': 0} %}
                {% for scenario in scenarios.values() %}
                    {% set priority = scenario.get('优先级', '中') %}
                    {% set _ = priority_counts.update({priority: priority_counts[priority] + 1}) %}
                {% endfor %}
                
                {% for priority, count in priority_counts.items() %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{{ priority }}优先级</span>
                    <span class="badge {% if priority == '高' %}bg-danger{% elif priority == '中' %}bg-warning{% else %}bg-secondary{% endif %}">
                        {{ count }}
                    </span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function generateForScenario(scenarioName) {
    showLoading('正在为 ' + scenarioName + ' 生成测试用例...');
    
    fetch('/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            scenario_name: scenarioName,
            custom_requirements: ''
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('测试用例生成成功！\n文件：' + data.filename);
            window.location.href = '/files';
        } else {
            alert('生成失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading();
        alert('请求失败：' + error);
    });
}

function generateAllScenarios() {
    if (confirm('确定要为所有场景批量生成测试用例吗？这可能需要较长时间。')) {
        alert('批量生成功能开发中...');
    }
}

function exportModule() {
    alert('模块导出功能开发中...');
}

function showModuleStats() {
    new bootstrap.Modal(document.getElementById('moduleStatsModal')).show();
}
</script>
{% endblock %}
