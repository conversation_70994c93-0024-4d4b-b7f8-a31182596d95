<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}跨境电商测试用例管理系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-flashlight me-2"></i>
                跨境电商测试用例管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('list_files') }}">
                            <i class="fas fa-file-alt me-1"></i>测试用例文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showAbout()">
                            <i class="fas fa-info-circle me-1"></i>关于
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-code me-1"></i>
                十年测试专家打造 | 专为跨境电商手电筒业务定制
            </p>
            <small class="text-muted">
                覆盖美国、德国、奥地利、英国、澳大利亚、意大利、加拿大、西班牙、法国、日本、韩国等12个国家
            </small>
        </div>
    </footer>

    <!-- 关于模态框 -->
    <div class="modal fade" id="aboutModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>关于系统
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6><i class="fas fa-star text-warning me-2"></i>系统特色</h6>
                    <ul>
                        <li><strong>专业性</strong>：十年测试专家经验，专为跨境电商定制</li>
                        <li><strong>智能化</strong>：基于ERNIE Bot AI，智能生成高质量测试用例</li>
                        <li><strong>分层展示</strong>：按功能模块分层展示，便于管理和查找</li>
                        <li><strong>国际化</strong>：覆盖12个国家的完整业务场景</li>
                    </ul>
                    
                    <h6><i class="fas fa-globe text-primary me-2"></i>覆盖国家</h6>
                    <div class="row">
                        <div class="col-6">
                            <span class="badge bg-primary me-1">🇺🇸 美国</span>
                            <span class="badge bg-primary me-1">🇩🇪 德国</span>
                            <span class="badge bg-primary me-1">🇦🇹 奥地利</span>
                            <span class="badge bg-primary me-1">🇬🇧 英国</span>
                            <span class="badge bg-primary me-1">🇦🇺 澳大利亚</span>
                            <span class="badge bg-primary me-1">🇮🇹 意大利</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-primary me-1">🇨🇦 加拿大</span>
                            <span class="badge bg-primary me-1">🇪🇸 西班牙</span>
                            <span class="badge bg-primary me-1">🇫🇷 法国</span>
                            <span class="badge bg-primary me-1">🇯🇵 日本</span>
                            <span class="badge bg-primary me-1">🇰🇷 韩国</span>
                        </div>
                    </div>
                    
                    <h6 class="mt-3"><i class="fas fa-cog text-success me-2"></i>技术栈</h6>
                    <p><strong>后端</strong>：Python Flask + ERNIE Bot SDK</p>
                    <p><strong>前端</strong>：Bootstrap 5 + Font Awesome</p>
                    <p><strong>AI引擎</strong>：百度ERNIE Bot大模型</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingModal" class="modal fade" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary me-2" role="status"></div>
                    <span id="loadingText">正在处理...</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <script>
        function showAbout() {
            new bootstrap.Modal(document.getElementById('aboutModal')).show();
        }
        
        function showLoading(text = '正在处理...') {
            document.getElementById('loadingText').textContent = text;
            new bootstrap.Modal(document.getElementById('loadingModal')).show();
        }
        
        function hideLoading() {
            bootstrap.Modal.getInstance(document.getElementById('loadingModal'))?.hide();
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
