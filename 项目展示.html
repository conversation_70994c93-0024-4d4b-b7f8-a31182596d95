<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔦 跨境电商手电筒业务 - 专业测试用例管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .feature-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .file-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .file-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .countries {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .country-tag {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .platforms {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .platform-card {
            background: white;
            border: 2px solid #667eea;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .platform-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }
        
        .highlight h2 {
            border: none;
            color: white;
            margin-bottom: 15px;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔦 跨境电商手电筒业务</h1>
            <p>专业测试用例管理系统 - 十年测试专家团队倾力打造</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🎯 项目成果展示</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div>覆盖国家</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div>支持平台</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">500+</div>
                        <div>测试用例</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">95%+</div>
                        <div>覆盖率</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🌍 国际化覆盖</h2>
                <div class="countries">
                    <span class="country-tag">🇺🇸 美国</span>
                    <span class="country-tag">🇩🇪 德国</span>
                    <span class="country-tag">🇦🇹 奥地利</span>
                    <span class="country-tag">🇬🇧 英国</span>
                    <span class="country-tag">🇦🇺 澳大利亚</span>
                    <span class="country-tag">🇮🇹 意大利</span>
                    <span class="country-tag">🇨🇦 加拿大</span>
                    <span class="country-tag">🇪🇸 西班牙</span>
                    <span class="country-tag">🇫🇷 法国</span>
                    <span class="country-tag">🇯🇵 日本</span>
                    <span class="country-tag">🇰🇷 韩国</span>
                </div>
            </div>
            
            <div class="section">
                <h2>📱 多平台支持</h2>
                <div class="platforms">
                    <div class="platform-card">
                        <div class="platform-icon">📱</div>
                        <h4>Android</h4>
                        <p>原生功能、推送通知</p>
                    </div>
                    <div class="platform-card">
                        <div class="platform-icon">🍎</div>
                        <h4>iOS</h4>
                        <p>Face ID、Apple Pay</p>
                    </div>
                    <div class="platform-card">
                        <div class="platform-icon">🌐</div>
                        <h4>H5</h4>
                        <p>轻量级、快速加载</p>
                    </div>
                    <div class="platform-card">
                        <div class="platform-icon">💻</div>
                        <h4>Web</h4>
                        <p>全功能、响应式</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🚀 核心功能特色</h2>
                <div class="features">
                    <div class="feature-card">
                        <h3>🤖 AI智能生成</h3>
                        <p>基于ERNIE Bot大模型，智能生成高质量测试用例，提升测试效率50%+</p>
                    </div>
                    <div class="feature-card">
                        <h3>📊 分层管理</h3>
                        <p>按功能模块分层展示，便于管理和查找，支持XMind可视化</p>
                    </div>
                    <div class="feature-card">
                        <h3>⚡ 复杂场景</h3>
                        <p>深度覆盖会员体系、优惠叠加、BNPL支付等复杂业务场景</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔍 专业测试</h3>
                        <p>十年测试专家经验，确保测试用例的专业性和实用性</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📄 已生成的测试文件</h2>
                <div class="file-list">
                    <div class="file-item">
                        <span class="file-icon">📋</span>
                        <div>
                            <strong>跨境电商手电筒业务_全面测试用例_v2.0.md</strong>
                            <br><small>完整的测试用例文档，包含所有业务场景</small>
                        </div>
                    </div>
                    <div class="file-item">
                        <span class="file-icon">🧠</span>
                        <div>
                            <strong>跨境电商手电筒业务_XMind测试用例结构.json</strong>
                            <br><small>XMind思维导图结构数据，可直接导入</small>
                        </div>
                    </div>
                    <div class="file-item">
                        <span class="file-icon">📊</span>
                        <div>
                            <strong>跨境电商手电筒_XMind导入用例.csv</strong>
                            <br><small>CSV格式测试用例，可直接导入XMind</small>
                        </div>
                    </div>
                    <div class="file-item">
                        <span class="file-icon">📝</span>
                        <div>
                            <strong>跨境电商手电筒_详细测试用例执行表.md</strong>
                            <br><small>详细的测试执行表，包含具体步骤和预期结果</small>
                        </div>
                    </div>
                    <div class="file-item">
                        <span class="file-icon">🔧</span>
                        <div>
                            <strong>Flask Web管理系统</strong>
                            <br><small>完整的Web界面，支持分层展示和AI生成</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="highlight">
                <h2>🎉 核心业务场景覆盖</h2>
                <p><strong>会员体系</strong>：游客、普通会员、小B会员权限验证</p>
                <p><strong>优惠系统</strong>：优惠券、优惠码、O币抵扣复杂叠加</p>
                <p><strong>BNPL支付</strong>：先享后付、风控机制、分期管理</p>
                <p><strong>多平台一致性</strong>：数据同步、功能一致、性能优化</p>
                <p><strong>国际化合规</strong>：GDPR、CCPA、多语言、多货币</p>
            </div>
            
            <div class="section">
                <h2>🔧 使用指南</h2>
                <div class="features">
                    <div class="feature-card">
                        <h3>1. 启动Web系统</h3>
                        <p>运行 <code>python app.py</code> 启动Flask Web界面</p>
                    </div>
                    <div class="feature-card">
                        <h3>2. 导入XMind</h3>
                        <p>使用CSV文件直接导入XMind创建思维导图</p>
                    </div>
                    <div class="feature-card">
                        <h3>3. AI生成用例</h3>
                        <p>运行增强版生成器创建新的测试用例</p>
                    </div>
                    <div class="feature-card">
                        <h3>4. 团队协作</h3>
                        <p>使用分层管理系统进行团队协作</p>
                    </div>
                </div>
                
                <div style="margin-top: 30px;">
                    <a href="#" class="btn" onclick="alert('运行命令：python app.py')">启动Web系统</a>
                    <a href="#" class="btn" onclick="alert('运行命令：python scripts/test_case_generator_enhanced.py')">AI生成用例</a>
                    <a href="#" class="btn" onclick="alert('查看README.md获取详细使用指南')">查看文档</a>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🔦 <strong>十年测试专家团队倾力打造</strong></p>
            <p>专为跨境电商手电筒业务定制的专业测试用例管理系统</p>
            <p>提升测试效率 | 保证产品质量 | 支持团队协作</p>
        </div>
    </div>
</body>
</html>
