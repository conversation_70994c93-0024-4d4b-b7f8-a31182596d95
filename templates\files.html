{% extends "base.html" %}

{% block title %}测试用例文件 - 跨境电商测试用例管理系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h1 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    测试用例文件管理
                </h1>
                <p class="card-text mt-2 mb-0">
                    管理和下载已生成的测试用例文件
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 文件统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ files|length }}</h4>
                <small class="text-muted">总文件数</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">
                    {{ files|selectattr('name', 'match', '.*\\.xlsx$')|list|length }}
                </h4>
                <small class="text-muted">Excel文件</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">
                    {{ files|selectattr('name', 'match', '.*\\.txt$')|list|length }}
                </h4>
                <small class="text-muted">文本文件</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">
                    {{ (files|map(attribute='size')|sum / 1024)|round(1) }}KB
                </h4>
                <small class="text-muted">总大小</small>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary" onclick="refreshFiles()">
                <i class="fas fa-sync-alt me-1"></i>
                刷新列表
            </button>
            <button type="button" class="btn btn-outline-success" onclick="downloadAll()">
                <i class="fas fa-download me-1"></i>
                批量下载
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="cleanupFiles()">
                <i class="fas fa-trash me-1"></i>
                清理文件
            </button>
        </div>
    </div>
</div>

<!-- 文件列表 -->
{% if files %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    文件列表
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-primary">
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="10%">类型</th>
                                <th width="40%">文件名</th>
                                <th width="10%">大小</th>
                                <th width="15%">修改时间</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for file in files %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="file-checkbox" value="{{ file.name }}">
                                </td>
                                <td>
                                    {% if file.name.endswith('.xlsx') %}
                                        <i class="fas fa-file-excel text-success" title="Excel文件"></i>
                                    {% elif file.name.endswith('.csv') %}
                                        <i class="fas fa-file-csv text-info" title="CSV文件"></i>
                                    {% elif file.name.endswith('.json') %}
                                        <i class="fas fa-file-code text-warning" title="JSON文件"></i>
                                    {% elif file.name.endswith('.txt') %}
                                        <i class="fas fa-file-alt text-secondary" title="文本文件"></i>
                                    {% else %}
                                        <i class="fas fa-file text-muted" title="其他文件"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <div class="fw-bold">{{ file.name }}</div>
                                            {% if '测试用例' in file.name %}
                                                <small class="text-muted">
                                                    <i class="fas fa-tag me-1"></i>测试用例
                                                </small>
                                            {% elif '对话历史' in file.name %}
                                                <small class="text-muted">
                                                    <i class="fas fa-comments me-1"></i>对话历史
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="file-size">
                                        {% if file.size < 1024 %}
                                            {{ file.size }}B
                                        {% elif file.size < 1048576 %}
                                            {{ (file.size / 1024)|round(1) }}KB
                                        {% else %}
                                            {{ (file.size / 1048576)|round(1) }}MB
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="file-date">{{ file.modified }}</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('download_file', filename=file.name) }}" 
                                           class="btn btn-outline-primary" title="下载">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="previewFile('{{ file.name }}')" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteFile('{{ file.name }}')" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- 无文件时的提示 -->
<div class="row">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-folder-open fa-3x text-warning mb-3"></i>
                <h4>暂无测试用例文件</h4>
                <p class="text-muted">还没有生成任何测试用例文件，您可以：</p>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-magic me-1"></i>
                        生成测试用例
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="uploadFile()">
                        <i class="fas fa-upload me-1"></i>
                        上传文件
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 文件预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>
                    文件预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载文件内容...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="downloadPreviewFile">
                    <i class="fas fa-download me-1"></i>
                    下载文件
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let currentPreviewFile = '';

function refreshFiles() {
    window.location.reload();
}

function downloadAll() {
    const checkboxes = document.querySelectorAll('.file-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('请选择要下载的文件');
        return;
    }
    
    checkboxes.forEach(checkbox => {
        const filename = checkbox.value;
        const link = document.createElement('a');
        link.href = '/download/' + filename;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

function cleanupFiles() {
    const checkboxes = document.querySelectorAll('.file-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('请选择要删除的文件');
        return;
    }
    
    if (confirm('确定要删除选中的 ' + checkboxes.length + ' 个文件吗？此操作不可恢复。')) {
        alert('批量删除功能开发中...');
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.file-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function previewFile(filename) {
    currentPreviewFile = filename;
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
    
    // 设置下载按钮
    document.getElementById('downloadPreviewFile').onclick = function() {
        window.open('/download/' + filename);
    };
    
    // 加载文件内容（这里简化处理）
    document.getElementById('previewContent').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>文件名：</strong>${filename}<br>
            <strong>说明：</strong>文件预览功能开发中，请下载文件查看完整内容。
        </div>
        <div class="text-center">
            <a href="/download/${filename}" class="btn btn-primary">
                <i class="fas fa-download me-1"></i>
                立即下载
            </a>
        </div>
    `;
}

function deleteFile(filename) {
    if (confirm('确定要删除文件 "' + filename + '" 吗？此操作不可恢复。')) {
        alert('删除文件功能开发中：' + filename);
    }
}

function uploadFile() {
    alert('文件上传功能开发中...');
}

// 文件大小格式化
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + 'B';
    if (bytes < 1048576) return (bytes / 1024).toFixed(1) + 'KB';
    return (bytes / 1048576).toFixed(1) + 'MB';
}
</script>
{% endblock %}
