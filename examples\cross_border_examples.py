"""
跨境电商测试用例生成示例
包含典型的跨境电商业务场景
"""
from test_case_generator import CrossBorderTestGenerator
import os


def example_user_registration():
    """示例1：用户注册功能"""
    requirement = """
跨境电商用户注册功能需求：

基本功能：
1. 支持邮箱注册，需要邮箱验证
2. 支持手机号注册，需要短信验证（支持国际手机号）
3. 支持社交账号登录（Google、Facebook、Apple ID）

密码要求：
- 长度：8-20位字符
- 复杂度：必须包含大小写字母、数字
- 特殊字符：可选但推荐

多语言支持：
- 界面语言：英语、日语、德语、法语、西班牙语
- 错误提示：多语言错误信息
- 邮件模板：多语言邮件验证模板

站点选择：
- 美国站（amazon.com）
- 欧洲站（amazon.de, amazon.fr, amazon.it等）
- 日本站（amazon.co.jp）
- 澳洲站（amazon.com.au）

合规要求：
- GDPR合规：欧盟用户需要明确同意数据处理
- CCPA合规：加州用户需要隐私权说明
- 年龄验证：部分地区需要年龄确认

业务规则：
- 同一邮箱只能注册一个账户
- 手机号可以绑定多个站点账户
- 注册后自动创建对应站点的购物车和愿望清单
"""
    
    return requirement


def example_multi_currency_payment():
    """示例2：多货币支付功能"""
    requirement = """
跨境电商多货币支付系统需求：

支持货币：
- 美元（USD）- 美国站主货币
- 欧元（EUR）- 欧洲站主货币  
- 英镑（GBP）- 英国站货币
- 日元（JPY）- 日本站货币
- 澳元（AUD）- 澳洲站货币
- 人民币（CNY）- 中国用户支付

汇率处理：
- 实时汇率获取（每15分钟更新）
- 汇率波动保护（订单确认后汇率锁定30分钟）
- 汇率显示精度（小数点后4位）
- 支付金额精度（小数点后2位）

支付方式：
- 信用卡：Visa、MasterCard、American Express、JCB
- 数字钱包：PayPal、Apple Pay、Google Pay、Amazon Pay
- 本地支付：
  * 美国：Stripe、Square
  * 欧洲：SEPA、iDEAL、Sofort、Klarna
  * 日本：JCB、银联、便利店支付
  * 澳洲：BPAY、POLi

税费计算：
- 美国：各州销售税（0-10.75%）
- 欧盟：VAT税（19-27%）
- 日本：消费税（10%）
- 澳洲：GST税（10%）

风控规则：
- 单笔限额：根据用户等级和地区设定
- 日累计限额：防止洗钱和欺诈
- 异常交易检测：地理位置、设备指纹、行为模式
- 3D验证：欧洲地区强制要求
"""
    
    return requirement


def example_cross_border_logistics():
    """示例3：跨境物流功能"""
    requirement = """
跨境电商物流管理系统需求：

物流模式：
1. 海外仓发货（FBA模式）
   - 美国仓：覆盖美国、加拿大、墨西哥
   - 欧洲仓：覆盖欧盟27国、英国、瑞士
   - 日本仓：覆盖日本全境
   - 澳洲仓：覆盖澳大利亚、新西兰

2. 直邮模式
   - 中国直邮：通过EMS、DHL、FedEx等
   - 时效：7-15个工作日
   - 清关：自动清关服务

配送时效：
- 标准配送：3-7个工作日
- 快速配送：1-3个工作日  
- 次日达：限定城市，当日18:00前下单
- 当日达：限定城市，当日12:00前下单

运费计算：
- 重量计费：按实际重量和体积重量取大值
- 分区计费：按目的地邮编分区
- 免邮政策：订单金额超过阈值免运费
- 运费补贴：新用户、会员优惠

包裹限制：
- 重量限制：单件最大30kg
- 尺寸限制：长+宽+高不超过150cm
- 禁运商品：液体、电池、食品、药品等
- 海关申报：自动生成申报单据

物流追踪：
- 全程追踪：从发货到签收
- 多语言状态：根据用户语言显示
- 异常处理：延误、丢失、损坏处理流程
- 签收确认：电子签名、拍照确认

清关服务：
- 自动报关：系统自动生成报关单
- 税费代缴：代缴进口关税和增值税
- 清关时效：正常1-3个工作日
- 异常处理：查验、退运处理流程
"""
    
    return requirement


def example_compliance_testing():
    """示例4：合规性测试"""
    requirement = """
跨境电商合规性管理需求：

数据保护合规：
1. GDPR合规（欧盟）
   - Cookie同意机制
   - 数据处理透明度
   - 用户权利：访问、更正、删除、可携带
   - 数据保护官（DPO）联系方式
   - 数据泄露通知（72小时内）

2. CCPA合规（加利福尼亚）
   - 隐私政策透明度
   - 消费者权利：知情权、删除权、选择退出权
   - "不出售个人信息"选项
   - 第三方数据共享声明

税务合规：
1. 美国销售税
   - Nexus规则：各州经济关联标准
   - 税率计算：州税+地方税
   - 免税证书：B2B交易免税处理
   - 税务报告：月度/季度申报

2. 欧盟VAT
   - OSS制度：一站式服务
   - VAT号验证：VIES系统验证
   - B2B免税：有效VAT号免税
   - 发票要求：符合各国发票标准

产品合规：
1. CE认证（欧盟）
   - 适用产品：电子产品、玩具、医疗器械
   - 符合性声明：DoC文件
   - 技术文档：保存10年
   - 授权代表：欧盟境内代表

2. FCC认证（美国）
   - 设备授权：无线电设备
   - 符合性测试：EMC测试
   - 标识要求：FCC ID标识
   - 进口商责任：符合性声明

反洗钱（AML）：
- 客户身份识别（KYC）
- 大额交易报告：>$10,000
- 可疑交易报告：异常模式识别
- 制裁名单筛查：OFAC、UN等

年龄验证：
- 酒类：21岁以上（美国）、18岁以上（欧盟）
- 烟草：21岁以上（美国）、18岁以上（欧盟）
- 成人用品：18岁以上
- 验证方式：身份证件、信用卡验证
"""
    
    return requirement


def run_examples():
    """运行所有示例"""
    print("🌍 跨境电商测试用例生成示例")
    print("=" * 50)
    
    # 检查是否设置了Access Token
    if not os.getenv('ERNIE_ACCESS_TOKEN'):
        print("❌ 请先设置ERNIE_ACCESS_TOKEN环境变量")
        print("   可以创建.env文件并添加：ERNIE_ACCESS_TOKEN=your_token")
        return
    
    try:
        # 创建生成器
        generator = CrossBorderTestGenerator()
        
        examples = [
            ("用户注册功能", example_user_registration()),
            ("多货币支付功能", example_multi_currency_payment()),
            ("跨境物流功能", example_cross_border_logistics()),
            ("合规性测试", example_compliance_testing())
        ]
        
        for i, (title, requirement) in enumerate(examples, 1):
            print(f"\n📝 示例 {i}：{title}")
            print("-" * 30)
            
            # 生成测试用例
            test_cases = generator.generate_test_cases(
                requirement=requirement,
                test_type="功能测试",
                priority="高",
                output_format="表格"
            )
            
            print(test_cases[:500] + "..." if len(test_cases) > 500 else test_cases)
            
            # 导出为Excel
            excel_file = generator.export_test_cases(test_cases, "excel", f"example_{i}_{title}.xlsx")
            print(f"✅ 已导出到：{excel_file}")
            
            print("\n" + "="*50)
    
    except Exception as e:
        print(f"❌ 运行示例失败：{str(e)}")


if __name__ == "__main__":
    run_examples()
