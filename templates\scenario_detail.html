{% extends "base.html" %}

{% block title %}{{ scenario_name }} - 测试用例详情{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{{ url_for('index') }}">
                <i class="fas fa-home me-1"></i>首页
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ url_for('index') }}">测试模块</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            {{ scenario_name }}
        </li>
    </ol>
</nav>

<!-- 场景标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h1 class="card-title mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    {{ scenario_name }} - 测试用例
                </h1>
                <p class="card-text mt-2 mb-0">
                    {% if test_cases %}
                        包含 {{ test_cases|length }} 个测试用例模板
                    {% else %}
                        暂无测试用例模板，可使用AI生成
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-primary" onclick="generateTestCases()">
                <i class="fas fa-magic me-1"></i>
                AI生成测试用例
            </button>
            <button type="button" class="btn btn-outline-success" onclick="exportTestCases()">
                <i class="fas fa-download me-1"></i>
                导出用例
            </button>
            <button type="button" class="btn btn-outline-info" onclick="showTemplateModal()">
                <i class="fas fa-plus me-1"></i>
                添加模板
            </button>
        </div>
    </div>
</div>

<!-- 测试用例列表 -->
{% if test_cases %}
<div class="row">
    {% for test_case in test_cases %}
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <span class="badge bg-primary me-2">{{ test_case.get('用例编号', 'TC_XXX') }}</span>
                    {{ test_case.get('用例标题', '未命名测试用例') }}
                </h5>
                <span class="badge {% if test_case.get('优先级') == '高' %}bg-danger{% elif test_case.get('优先级') == '中' %}bg-warning{% else %}bg-secondary{% endif %}">
                    {{ test_case.get('优先级', '中') }}优先级
                </span>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">
                            <i class="fas fa-info-circle me-1"></i>
                            基本信息
                        </h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>测试模块：</strong></td>
                                <td>{{ test_case.get('测试模块', '未指定') }}</td>
                            </tr>
                            <tr>
                                <td><strong>测试场景：</strong></td>
                                <td>{{ test_case.get('测试场景', scenario_name) }}</td>
                            </tr>
                            <tr>
                                <td><strong>前置条件：</strong></td>
                                <td>{{ test_case.get('前置条件', '无') }}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-database me-1"></i>
                            测试数据
                        </h6>
                        {% if test_case.get('测试数据') %}
                        <div class="bg-light p-2 rounded">
                            {% for key, value in test_case['测试数据'].items() %}
                            <small><strong>{{ key }}：</strong>{{ value }}</small><br>
                            {% endfor %}
                        </div>
                        {% else %}
                        <small class="text-muted">无特定测试数据</small>
                        {% endif %}
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-warning">
                            <i class="fas fa-list-ol me-1"></i>
                            测试步骤
                        </h6>
                        {% if test_case.get('测试步骤') %}
                        <ol class="small">
                            {% for step in test_case['测试步骤'] %}
                            <li>{{ step }}</li>
                            {% endfor %}
                        </ol>
                        {% else %}
                        <p class="text-muted small">无详细测试步骤</p>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-info">
                            <i class="fas fa-check-circle me-1"></i>
                            预期结果
                        </h6>
                        {% if test_case.get('预期结果') %}
                        <ol class="small">
                            {% for result in test_case['预期结果'] %}
                            <li>{{ result }}</li>
                            {% endfor %}
                        </ol>
                        {% else %}
                        <p class="text-muted small">无详细预期结果</p>
                        {% endif %}
                    </div>
                </div>
                
                {% if test_case.get('备注') %}
                <div class="alert alert-info mt-3">
                    <i class="fas fa-sticky-note me-1"></i>
                    <strong>备注：</strong>{{ test_case['备注'] }}
                </div>
                {% endif %}
            </div>
            
            <div class="card-footer bg-transparent">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="editTestCase('{{ test_case.get('用例编号', '') }}')">
                        <i class="fas fa-edit me-1"></i>
                        编辑
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="copyTestCase('{{ test_case.get('用例编号', '') }}')">
                        <i class="fas fa-copy me-1"></i>
                        复制
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteTestCase('{{ test_case.get('用例编号', '') }}')">
                        <i class="fas fa-trash me-1"></i>
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<!-- 无测试用例时的提示 -->
<div class="row">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4>暂无测试用例模板</h4>
                <p class="text-muted">该场景还没有预定义的测试用例模板，您可以：</p>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary" onclick="generateTestCases()">
                        <i class="fas fa-magic me-1"></i>
                        使用AI生成测试用例
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="showTemplateModal()">
                        <i class="fas fa-plus me-1"></i>
                        手动添加模板
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 添加模板模态框 -->
<div class="modal fade" id="templateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>添加测试用例模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="caseId" class="form-label">用例编号</label>
                                <input type="text" class="form-control" id="caseId" placeholder="TC_XXX_001">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="caseTitle" class="form-label">用例标题</label>
                                <input type="text" class="form-control" id="caseTitle" placeholder="测试用例标题">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="casePriority" class="form-label">优先级</label>
                                <select class="form-select" id="casePriority">
                                    <option value="高">高</option>
                                    <option value="中" selected>中</option>
                                    <option value="低">低</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="caseModule" class="form-label">测试模块</label>
                                <input type="text" class="form-control" id="caseModule" value="{{ scenario_name }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="casePrecondition" class="form-label">前置条件</label>
                        <textarea class="form-control" id="casePrecondition" rows="2" placeholder="执行测试前需要满足的条件"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="caseSteps" class="form-label">测试步骤</label>
                        <textarea class="form-control" id="caseSteps" rows="4" placeholder="1. 第一步操作&#10;2. 第二步操作&#10;3. 第三步操作"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="caseExpected" class="form-label">预期结果</label>
                        <textarea class="form-control" id="caseExpected" rows="4" placeholder="1. 预期结果一&#10;2. 预期结果二&#10;3. 预期结果三"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="caseRemark" class="form-label">备注</label>
                        <textarea class="form-control" id="caseRemark" rows="2" placeholder="其他说明或注意事项"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTemplate()">
                    <i class="fas fa-save me-1"></i>
                    保存模板
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function generateTestCases() {
    showLoading('正在为 {{ scenario_name }} 生成测试用例...');
    
    fetch('/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            scenario_name: '{{ scenario_name }}',
            custom_requirements: ''
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('测试用例生成成功！\n文件：' + data.filename);
            window.location.href = '/files';
        } else {
            alert('生成失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading();
        alert('请求失败：' + error);
    });
}

function exportTestCases() {
    alert('导出功能开发中...');
}

function showTemplateModal() {
    new bootstrap.Modal(document.getElementById('templateModal')).show();
}

function saveTemplate() {
    alert('保存模板功能开发中...');
}

function editTestCase(caseId) {
    alert('编辑用例功能开发中：' + caseId);
}

function copyTestCase(caseId) {
    alert('复制用例功能开发中：' + caseId);
}

function deleteTestCase(caseId) {
    if (confirm('确定要删除用例 ' + caseId + ' 吗？')) {
        alert('删除用例功能开发中：' + caseId);
    }
}
</script>
{% endblock %}
