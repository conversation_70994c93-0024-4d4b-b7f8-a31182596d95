{"测试用例模板": {"邮箱注册": [{"用例编号": "TC_REG_EMAIL_001", "用例标题": "有效邮箱注册-美国站", "测试模块": "用户注册", "测试场景": "邮箱注册", "优先级": "高", "前置条件": "用户未注册，访问注册页面", "测试步骤": ["1. 选择美国站点", "2. 输入有效邮箱地址（如：<EMAIL>）", "3. 设置符合要求的密码（8-20位，包含大小写字母和数字）", "4. 确认密码", "5. 同意服务条款和隐私政策", "6. 点击注册按钮"], "预期结果": ["1. 注册成功", "2. 发送邮箱验证邮件", "3. 跳转到邮箱验证提示页面", "4. 显示美国站欢迎信息"], "测试数据": {"邮箱": "<EMAIL>", "密码": "TestPass123", "站点": "美国站"}, "备注": "验证美国站邮箱注册基本流程"}, {"用例编号": "TC_REG_EMAIL_002", "用例标题": "无效邮箱格式注册", "测试模块": "用户注册", "测试场景": "邮箱注册", "优先级": "高", "前置条件": "用户未注册，访问注册页面", "测试步骤": ["1. 选择任意站点", "2. 输入无效邮箱格式（如：invalid-email）", "3. 设置密码", "4. 点击注册按钮"], "预期结果": ["1. 显示邮箱格式错误提示", "2. 注册按钮保持不可用状态", "3. 不发送注册请求"], "测试数据": {"邮箱": "invalid-email", "密码": "TestPass123"}, "备注": "验证邮箱格式校验功能"}], "手机号注册": [{"用例编号": "TC_REG_PHONE_001", "用例标题": "有效手机号注册-德国站", "测试模块": "用户注册", "测试场景": "手机号注册", "优先级": "高", "前置条件": "用户未注册，访问注册页面", "测试步骤": ["1. 选择德国站点", "2. 选择手机号注册方式", "3. 输入德国手机号（+49开头）", "4. 设置密码", "5. 点击获取验证码", "6. 输入收到的短信验证码", "7. 同意GDPR数据处理协议", "8. 点击注册按钮"], "预期结果": ["1. 成功发送短信验证码", "2. 验证码验证成功", "3. 注册成功", "4. 显示GDPR合规提示", "5. 跳转到德国站首页"], "测试数据": {"手机号": "+4915123456789", "密码": "TestPass123", "站点": "德国站"}, "备注": "验证德国站手机号注册和GDPR合规"}], "第三方登录": [{"用例编号": "TC_LOGIN_GOOGLE_001", "用例标题": "Google账号登录", "测试模块": "用户登录", "测试场景": "第三方登录", "优先级": "中", "前置条件": "用户有有效的Google账号", "测试步骤": ["1. 访问登录页面", "2. 点击'使用Google登录'按钮", "3. 跳转到Google授权页面", "4. 输入Google账号和密码", "5. 确认授权应用访问基本信息", "6. 返回到应用页面"], "预期结果": ["1. 成功跳转到Google授权页面", "2. Google账号验证成功", "3. 授权成功后返回应用", "4. 自动登录成功", "5. 显示用户基本信息"], "测试数据": {"Google账号": "<EMAIL>", "授权范围": "基本信息、邮箱地址"}, "备注": "验证Google OAuth2.0集成"}], "站点切换": [{"用例编号": "TC_SITE_SWITCH_001", "用例标题": "美国站切换到日本站", "测试模块": "用户登录", "测试场景": "站点切换", "优先级": "中", "前置条件": "用户已登录美国站", "测试步骤": ["1. 点击站点切换按钮", "2. 选择日本站", "3. 确认切换操作", "4. 等待页面刷新"], "预期结果": ["1. 成功切换到日本站", "2. 界面语言切换为日语", "3. 货币显示为日元（JPY）", "4. 商品价格按日本站定价显示", "5. 用户登录状态保持"], "测试数据": {"原站点": "美国站", "目标站点": "日本站"}, "备注": "验证SSO单点登录和站点数据同步"}], "密码登录": [{"用例编号": "TC_LOGIN_PWD_001", "用例标题": "正确用户名密码登录", "测试模块": "用户登录", "测试场景": "密码登录", "优先级": "高", "前置条件": "用户已注册并激活账户", "测试步骤": ["1. 访问登录页面", "2. 输入正确的用户名/邮箱", "3. 输入正确的密码", "4. 点击登录按钮"], "预期结果": ["1. 登录验证成功", "2. 跳转到用户首页", "3. 显示用户欢迎信息", "4. 更新最后登录时间"], "测试数据": {"用户名": "test_user_001", "密码": "TestPass123"}, "备注": "基础登录功能验证"}, {"用例编号": "TC_LOGIN_PWD_002", "用例标题": "密码错误3次锁定", "测试模块": "用户登录", "测试场景": "密码登录", "优先级": "高", "前置条件": "用户已注册，账户未锁定", "测试步骤": ["1. 访问登录页面", "2. 输入正确的用户名", "3. 输入错误密码，点击登录（第1次）", "4. 输入错误密码，点击登录（第2次）", "5. 输入错误密码，点击登录（第3次）"], "预期结果": ["1. 前2次显示密码错误提示", "2. 第3次后显示图形验证码", "3. 继续错误则锁定账户15分钟", "4. 发送安全提醒邮件"], "测试数据": {"用户名": "test_user_002", "错误密码": "WrongPass123"}, "备注": "验证账户安全机制"}], "安全机制": [{"用例编号": "TC_SEC_LOGIN_001", "用例标题": "异地登录检测", "测试模块": "用户登录", "测试场景": "安全机制", "优先级": "高", "前置条件": "用户在美国IP地址已登录", "测试步骤": ["1. 使用德国IP地址访问登录页面", "2. 输入相同账户的用户名和密码", "3. 点击登录"], "预期结果": ["1. 检测到异地登录", "2. 要求额外身份验证（短信/邮箱验证码）", "3. 向原登录设备发送安全提醒", "4. 验证通过后允许登录"], "测试数据": {"原IP": "美国IP（*************）", "新IP": "德国IP（**********）"}, "备注": "验证地理位置安全检测"}]}}