# 🚀 快速开始指南

## 1. 获取Access Token（必需）

1. 访问 [百度AI Studio](https://aistudio.baidu.com/index/accessToken)
2. 登录百度账号
3. 在个人中心找到"访问令牌"
4. 复制Access Token

## 2. 配置Token

创建 `.env` 文件：
```bash
ERNIE_ACCESS_TOKEN=你的token在这里
```

## 3. 安装依赖

```bash
pip install -r requirements.txt
```

## 4. 启动系统

```bash
python start.py
```

选择 "1" 启动Web界面，或者直接运行：
```bash
python start.py --web
```

## 5. 使用示例

在Web界面中输入需求，例如：
```
跨境电商用户注册登录功能：
1. 支持邮箱和手机号注册
2. 多语言界面（中英日德）
3. 多站点选择（美国、欧洲、日本）
4. 社交登录（Google、Facebook）
5. 密码强度要求
6. GDPR合规
```

点击"生成测试用例"即可！

## 🎯 快速测试

如果想快速看效果，可以运行：
```bash
python start.py --examples
```

这会运行预设的跨境电商示例场景。
