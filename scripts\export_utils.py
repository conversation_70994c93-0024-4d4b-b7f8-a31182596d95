"""
测试用例导出工具
支持Excel、CSV、JSON等格式导出
"""
import pandas as pd
import json
import re
from datetime import datetime
from typing import List, Dict, Optional
import os


class TestCaseExporter:
    """测试用例导出器"""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = output_dir
        
    def parse_table_text(self, table_text: str) -> List[Dict]:
        """
        解析表格文本为结构化数据
        
        Args:
            table_text: 表格格式的文本
            
        Returns:
            解析后的数据列表
        """
        lines = table_text.strip().split('\n')
        data = []
        headers = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line or line.startswith('---') or line.startswith('==='):
                continue
                
            # 解析表格行
            if '|' in line:
                cells = [cell.strip() for cell in line.split('|')]
                cells = [cell for cell in cells if cell]  # 移除空单元格
                
                if i == 0 or not headers:  # 第一行作为表头
                    headers = cells
                else:
                    if len(cells) == len(headers):
                        row_data = dict(zip(headers, cells))
                        data.append(row_data)
        
        return data
    
    def export_to_excel(self, test_cases: str, filename: str = None) -> str:
        """
        导出为Excel格式
        
        Args:
            test_cases: 测试用例文本
            filename: 文件名
            
        Returns:
            导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_{timestamp}.xlsx"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 解析表格数据
        data = self.parse_table_text(test_cases)
        
        if data:
            df = pd.DataFrame(data)
            df.to_excel(filepath, index=False, engine='openpyxl')
        else:
            # 如果无法解析表格，直接保存文本
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df = pd.DataFrame({'测试用例内容': [test_cases]})
                df.to_excel(writer, sheet_name='测试用例', index=False)
        
        return filepath
    
    def export_to_csv(self, test_cases: str, filename: str = None) -> str:
        """
        导出为CSV格式
        
        Args:
            test_cases: 测试用例文本
            filename: 文件名
            
        Returns:
            导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_{timestamp}.csv"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 解析表格数据
        data = self.parse_table_text(test_cases)
        
        if data:
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
        else:
            # 如果无法解析表格，直接保存文本
            with open(filepath, 'w', encoding='utf-8-sig') as f:
                f.write(test_cases)
        
        return filepath
    
    def export_to_json(self, test_cases: str, filename: str = None) -> str:
        """
        导出为JSON格式
        
        Args:
            test_cases: 测试用例文本
            filename: 文件名
            
        Returns:
            导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 解析表格数据
        data = self.parse_table_text(test_cases)
        
        export_data = {
            "生成时间": datetime.now().isoformat(),
            "测试用例数量": len(data),
            "测试用例": data if data else [{"原始内容": test_cases}]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def export_to_xmind_format(self, test_cases: str, filename: str = None) -> str:
        """
        导出为XMind可导入的格式
        
        Args:
            test_cases: 测试用例文本
            filename: 文件名
            
        Returns:
            导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_xmind_{timestamp}.txt"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # 解析数据并转换为XMind格式
        data = self.parse_table_text(test_cases)
        
        xmind_content = "跨境电商测试用例\n"
        
        if data:
            # 按模块分组
            modules = {}
            for case in data:
                module = case.get('测试模块', case.get('模块', '未分类'))
                if module not in modules:
                    modules[module] = []
                modules[module].append(case)
            
            for module, cases in modules.items():
                xmind_content += f"\t{module}\n"
                for case in cases:
                    case_id = case.get('用例编号', case.get('编号', ''))
                    scenario = case.get('测试场景', case.get('场景', ''))
                    xmind_content += f"\t\t{case_id} {scenario}\n"
        else:
            xmind_content += f"\t{test_cases}\n"
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(xmind_content)
        
        return filepath


class ConversationManager:
    """对话管理器 - 支持多轮对话优化"""
    
    def __init__(self):
        self.conversation_history = []
    
    def add_message(self, role: str, content: str):
        """添加对话消息"""
        self.conversation_history.append({
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat()
        })
    
    def get_messages_for_api(self) -> List[Dict]:
        """获取API调用格式的消息列表"""
        return [
            {'role': msg['role'], 'content': msg['content']} 
            for msg in self.conversation_history
        ]
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def export_conversation(self, filename: str = None) -> str:
        """导出对话历史"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"
        
        filepath = os.path.join("output", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.conversation_history, f, ensure_ascii=False, indent=2)
        
        return filepath


# 使用示例
if __name__ == "__main__":
    # 示例表格文本
    sample_table = """
| 用例编号 | 测试模块 | 测试场景 | 预期结果 | 优先级 |
|----------|----------|----------|----------|--------|
| TC001    | 用户注册 | 邮箱注册 | 注册成功 | 高     |
| TC002    | 用户注册 | 手机注册 | 注册成功 | 高     |
| TC003    | 用户登录 | 密码登录 | 登录成功 | 高     |
"""
    
    exporter = TestCaseExporter()
    
    # 导出为不同格式
    excel_file = exporter.export_to_excel(sample_table)
    csv_file = exporter.export_to_csv(sample_table)
    json_file = exporter.export_to_json(sample_table)
    xmind_file = exporter.export_to_xmind_format(sample_table)
    
    print(f"Excel文件: {excel_file}")
    print(f"CSV文件: {csv_file}")
    print(f"JSON文件: {json_file}")
    print(f"XMind格式: {xmind_file}")
