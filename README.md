# 🌍 跨境电商测试用例生成器

基于ERNIE Bot SDK开发的智能测试用例生成工具，专为跨境电商业务定制，支持多站点、多语言、多货币等复杂场景的测试用例自动生成。

## ✨ 特性

- 🤖 **智能生成**：基于ERNIE Bot大模型，理解复杂业务需求
- 🌐 **跨境专业**：内置跨境电商业务知识库，覆盖多国站点规则
- 📊 **多格式导出**：支持Excel、CSV、JSON、XMind等格式
- 🔄 **多轮优化**：支持对话式优化，持续改进测试用例质量
- 🎨 **可视化界面**：提供Gradio Web界面，操作简单直观
- ⚡ **命令行工具**：支持批量处理和自动化集成

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd cross-border-test-generator

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置Access Token

从[百度AI Studio](https://aistudio.baidu.com/index/accessToken)获取Access Token：

```bash
# 方式1：创建.env文件
cp .env.example .env
# 编辑.env文件，填入你的Access Token

# 方式2：设置环境变量
export ERNIE_ACCESS_TOKEN=your_access_token_here
```

### 3. 使用方式

#### 🎨 Web界面（推荐）

```bash
python web_interface.py
```

访问 http://localhost:7860 使用可视化界面。

#### 💻 命令行工具

```bash
# 基本使用
python cli_tool.py --requirement "用户注册功能需求..." --export excel

# 运行示例
python cli_tool.py --examples

# 优化测试用例
python cli_tool.py --requirement "需求描述" --optimize "增加边界值测试" --export excel
```

#### 📝 Python脚本

```python
from test_case_generator import CrossBorderTestGenerator

# 创建生成器
generator = CrossBorderTestGenerator()

# 生成测试用例
test_cases = generator.generate_test_cases(
    requirement="用户注册功能需求...",
    test_type="功能测试",
    priority="高",
    output_format="表格"
)

# 导出Excel
excel_file = generator.export_test_cases(test_cases, "excel")
print(f"导出成功：{excel_file}")
```

## 📚 业务知识库

系统内置丰富的跨境电商业务知识：

### 🌎 多站点支持
- **美国站**：USD货币、销售税、FBA物流、FDA/FCC认证
- **欧洲站**：EUR/GBP货币、VAT税务、CE认证、GDPR合规
- **日本站**：JPY货币、消费税、PSE认证、本土物流
- **澳洲站**：AUD货币、GST税务、ACMA认证

### 💳 支付方式
- **信用卡**：Visa、MasterCard、American Express、JCB
- **数字钱包**：PayPal、Apple Pay、Google Pay
- **本地支付**：SEPA、iDEAL、Sofort、BPAY等

### 🚚 物流规则
- **配送时效**：标准配送、快速配送、次日达
- **重量限制**：小包裹、标准包裹、大件商品
- **禁运商品**：液体、易燃品、电池类、食品

### ⚖️ 合规要求
- **数据保护**：GDPR、CCPA合规
- **税务合规**：VAT、销售税计算
- **产品认证**：CE、FCC、PSE认证
- **反洗钱**：KYC、大额交易报告

## 🎯 测试场景覆盖

### 功能测试
- ✅ 用户注册/登录（多语言、多站点）
- ✅ 商品管理（多语言商品、库存同步）
- ✅ 订单处理（多货币结算、税费计算）
- ✅ 支付系统（多支付方式、风控检测）
- ✅ 物流管理（跨境物流、清关服务）

### 边界值测试
- ✅ 价格范围（0.01 - 999999.99）
- ✅ 数量限制（1 - 999）
- ✅ 用户输入长度限制
- ✅ 货币精度处理

### 异常场景
- ✅ 网络异常（超时、中断、弱网）
- ✅ 系统异常（服务器宕机、数据库故障）
- ✅ 业务异常（库存不足、支付失败、汇率异常）

### 合规性测试
- ✅ GDPR数据保护合规
- ✅ 税务计算准确性
- ✅ 产品认证验证
- ✅ 年龄验证机制

## 📁 项目结构

```
cross-border-test-generator/
├── config.py                 # 配置文件
├── test_case_generator.py     # 核心生成器
├── export_utils.py           # 导出工具
├── web_interface.py          # Web界面
├── cli_tool.py              # 命令行工具
├── knowledge_base/          # 业务知识库
│   ├── cross_border_rules.json
│   ├── test_scenarios.json
│   └── compliance_rules.json
├── examples/               # 示例代码
│   └── cross_border_examples.py
├── output/                # 输出文件目录
├── requirements.txt       # 依赖包
├── .env.example          # 环境变量示例
└── README.md            # 说明文档
```

## 🔧 高级功能

### 多轮对话优化

```python
# 生成初始测试用例
test_cases = generator.generate_test_cases(requirement)

# 多轮优化
optimized_cases = generator.optimize_test_cases(
    test_cases, 
    "增加弱网测试和并发场景"
)
```

### 批量导出

```python
# 导出多种格式
formats = ['excel', 'csv', 'json', 'xmind']
for fmt in formats:
    file_path = generator.export_test_cases(test_cases, fmt)
    print(f"{fmt.upper()}文件：{file_path}")
```

### 对话历史管理

```python
# 导出对话历史
conversation_file = generator.export_conversation()

# 清空对话历史
generator.clear_conversation()
```

## 📊 输出示例

生成的测试用例包含以下字段：

| 用例编号 | 测试模块 | 测试场景 | 前置条件 | 操作步骤 | 预期结果 | 优先级 | 备注 |
|----------|----------|----------|----------|----------|----------|--------|------|
| TC001 | 用户注册 | 邮箱注册-美国站 | 无 | 1.访问注册页面<br>2.选择美国站<br>3.输入邮箱密码 | 注册成功，收到验证邮件 | 高 | 多站点测试 |

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 🆘 常见问题

### Q: 如何获取ERNIE Access Token？
A: 访问[百度AI Studio](https://aistudio.baidu.com/index/accessToken)，登录后在个人中心获取访问令牌。

### Q: 生成的测试用例质量如何提升？
A: 1) 提供详细的需求描述；2) 使用多轮对话优化；3) 结合业务知识库补充特定场景。

### Q: 支持哪些导出格式？
A: 支持Excel(.xlsx)、CSV(.csv)、JSON(.json)、XMind格式(.txt)等多种格式。

### Q: 如何添加自定义业务规则？
A: 编辑`knowledge_base/`目录下的JSON文件，添加特定的业务规则和测试场景。

---

🌟 **如果这个项目对你有帮助，请给个Star支持一下！**
