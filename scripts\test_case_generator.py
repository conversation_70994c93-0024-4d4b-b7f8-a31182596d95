"""
跨境电商测试用例生成器
基于ERNIE Bot SDK实现
"""
import json
import os
import erniebot
from datetime import datetime
from config import Config
from typing import List, Dict, Optional
from export_utils import TestCaseExporter, ConversationManager


class CrossBorderTestGenerator:
    """跨境电商测试用例生成器"""
    
    def __init__(self):
        """初始化生成器"""
        Config.validate()

        # 配置ERNIE Bot
        erniebot.api_type = Config.ERNIE_API_TYPE
        erniebot.access_token = Config.ERNIE_ACCESS_TOKEN

        # 加载知识库
        self.knowledge_base = self._load_knowledge_base()

        # 初始化导出器和对话管理器
        self.exporter = TestCaseExporter(Config.OUTPUT_DIR)
        self.conversation = ConversationManager()
        
    def _load_knowledge_base(self) -> Dict:
        """加载知识库"""
        knowledge = {}
        kb_dir = Config.KNOWLEDGE_BASE_DIR
        
        for filename in os.listdir(kb_dir):
            if filename.endswith('.json'):
                with open(os.path.join(kb_dir, filename), 'r', encoding='utf-8') as f:
                    key = filename.replace('.json', '')
                    knowledge[key] = json.load(f)
        
        return knowledge
    
    def _build_context_prompt(self, business_domain: str = "跨境电商") -> str:
        """构建上下文提示词"""
        context = f"""
你是一名资深的{business_domain}测试工程师，具有丰富的国际化测试经验。

## 业务知识库：
"""
        
        # 添加相关知识库内容
        for kb_name, kb_content in self.knowledge_base.items():
            context += f"\n### {kb_name}:\n{json.dumps(kb_content, ensure_ascii=False, indent=2)}\n"
        
        return context
    
    def generate_test_cases(self, 
                          requirement: str, 
                          test_type: str = "功能测试",
                          priority: str = "全部",
                          output_format: str = "表格") -> str:
        """
        生成测试用例
        
        Args:
            requirement: 需求描述
            test_type: 测试类型（功能测试、接口测试、性能测试等）
            priority: 优先级过滤（高、中、低、全部）
            output_format: 输出格式（表格、XMind、JSON）
        
        Returns:
            生成的测试用例
        """
        
        context = self._build_context_prompt()
        
        prompt = f"""{context}

## 任务要求：
请基于以下需求生成{test_type}用例，重点关注跨境电商的特殊场景：

### 需求描述：
{requirement}

### 生成要求：
1. **输出格式**：{output_format}格式
2. **优先级**：{priority if priority != "全部" else "包含高、中、低三个优先级"}
3. **覆盖范围**：
   - 正向功能测试
   - 边界值测试
   - 异常场景测试
   - 跨境电商特有场景（多语言、多货币、多时区、合规性）
4. **测试用例字段**：
   - 用例编号（TC001格式）
   - 测试模块
   - 测试场景
   - 前置条件
   - 操作步骤
   - 预期结果
   - 优先级（高/中/低）
   - 备注（标注跨境特殊场景）

### 特别关注：
- 多站点兼容性（美国、欧洲、日本、澳洲）
- 多语言界面测试
- 多货币计算准确性
- 税务合规性（VAT、销售税）
- 数据保护合规（GDPR、CCPA）
- 跨时区业务处理
- 国际物流场景

请确保测试用例具有可执行性和完整性。
"""
        
        try:
            # 记录对话
            self.conversation.add_message('user', prompt)

            response = erniebot.ChatCompletion.create(
                model=Config.ERNIE_MODEL,
                messages=[{'role': 'user', 'content': prompt}]
            )

            result = response.get_result()
            self.conversation.add_message('assistant', result)

            return result
        except Exception as e:
            error_msg = f"生成失败：{str(e)}"
            self.conversation.add_message('assistant', error_msg)
            return error_msg
    
    def optimize_test_cases(self, 
                           original_cases: str, 
                           optimization_request: str) -> str:
        """
        优化测试用例（多轮对话）
        
        Args:
            original_cases: 原始测试用例
            optimization_request: 优化要求
        
        Returns:
            优化后的测试用例
        """
        
        messages = [
            {
                'role': 'user', 
                'content': f"请生成跨境电商测试用例：\n{original_cases}"
            },
            {
                'role': 'assistant', 
                'content': original_cases
            },
            {
                'role': 'user', 
                'content': f"请基于以下要求优化测试用例：\n{optimization_request}"
            }
        ]
        
        try:
            # 记录优化请求
            self.conversation.add_message('user', optimization_request)

            response = erniebot.ChatCompletion.create(
                model=Config.ERNIE_MODEL,
                messages=self.conversation.get_messages_for_api()
            )

            result = response.get_result()
            self.conversation.add_message('assistant', result)

            return result
        except Exception as e:
            error_msg = f"优化失败：{str(e)}"
            self.conversation.add_message('assistant', error_msg)
            return error_msg
    
    def save_test_cases(self, test_cases: str, filename: str = None) -> str:
        """
        保存测试用例到文件
        
        Args:
            test_cases: 测试用例内容
            filename: 文件名（可选）
        
        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_{timestamp}.txt"
        
        filepath = os.path.join(Config.OUTPUT_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(test_cases)
        
        return filepath

    def export_test_cases(self, test_cases: str, format_type: str = "excel", filename: str = None) -> str:
        """
        导出测试用例为指定格式

        Args:
            test_cases: 测试用例内容
            format_type: 导出格式（excel、csv、json、xmind）
            filename: 文件名

        Returns:
            导出文件路径
        """
        if format_type.lower() == "excel":
            return self.exporter.export_to_excel(test_cases, filename)
        elif format_type.lower() == "csv":
            return self.exporter.export_to_csv(test_cases, filename)
        elif format_type.lower() == "json":
            return self.exporter.export_to_json(test_cases, filename)
        elif format_type.lower() == "xmind":
            return self.exporter.export_to_xmind_format(test_cases, filename)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")

    def export_conversation(self, filename: str = None) -> str:
        """导出对话历史"""
        return self.conversation.export_conversation(filename)

    def clear_conversation(self):
        """清空对话历史"""
        self.conversation.clear_history()


# 使用示例
if __name__ == "__main__":
    # 创建生成器实例
    generator = CrossBorderTestGenerator()
    
    # 示例需求
    requirement = """
    跨境电商用户注册功能：
    1. 支持邮箱注册，需要邮箱验证
    2. 支持手机号注册，需要短信验证
    3. 密码要求：8-20位，包含大小写字母和数字
    4. 支持多语言界面（英语、日语、德语）
    5. 需要选择默认站点（美国、欧洲、日本、澳洲）
    6. 需要同意隐私政策和服务条款
    7. 支持Google、Facebook社交登录
    """
    
    # 生成测试用例
    print("正在生成测试用例...")
    test_cases = generator.generate_test_cases(
        requirement=requirement,
        test_type="功能测试",
        priority="全部",
        output_format="表格"
    )
    
    print("生成的测试用例：")
    print(test_cases)
    
    # 保存测试用例
    filepath = generator.save_test_cases(test_cases)
    print(f"\n测试用例已保存到：{filepath}")
