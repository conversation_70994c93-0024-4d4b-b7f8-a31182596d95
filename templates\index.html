{% extends "base.html" %}

{% block title %}首页 - 跨境电商测试用例管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 系统介绍 -->
        <div class="jumbotron bg-gradient-primary text-white p-5 rounded mb-4">
            <div class="container">
                <h1 class="display-4">
                    <i class="fas fa-flashlight me-3"></i>
                    跨境电商测试用例管理系统
                </h1>
                <p class="lead">
                    十年测试专家打造，专为手电筒跨境电商业务定制的专业测试用例管理平台
                </p>
                <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
                <p>
                    <i class="fas fa-globe me-2"></i>覆盖12个国家站点 
                    <i class="fas fa-robot ms-3 me-2"></i>AI智能生成 
                    <i class="fas fa-layer-group ms-3 me-2"></i>分层管理
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-sitemap me-2"></i>
            测试用例分层结构
        </h2>
    </div>
</div>

<div class="row">
    {% for module_name, module_info in structure.items() %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 shadow-sm hover-card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <span class="me-2" style="font-size: 1.2em;">{{ module_info.get('icon', '📁') }}</span>
                    {{ module_name }}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text text-muted">
                    {{ module_info.get('description', '暂无描述') }}
                </p>
                
                {% if module_info.get('子模块') %}
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-folder-open me-1"></i>
                        子模块 ({{ module_info['子模块']|length }})
                    </h6>
                    <div class="d-flex flex-wrap">
                        {% for sub_name, sub_info in module_info['子模块'].items() %}
                        <span class="badge bg-light text-dark me-1 mb-1">
                            {{ sub_info.get('icon', '📄') }} {{ sub_name }}
                        </span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="card-footer bg-transparent">
                <a href="{{ url_for('module_detail', module_path=module_name) }}" 
                   class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>
                    查看详情
                </a>
                <small class="text-muted ms-2">
                    {% if module_info.get('子模块') %}
                    {{ module_info['子模块']|length }} 个子模块
                    {% else %}
                    直接测试场景
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 快速操作 -->
<div class="row mt-5">
    <div class="col-12">
        <h3 class="mb-4">
            <i class="fas fa-bolt me-2"></i>
            快速操作
        </h3>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-magic fa-2x text-success mb-3"></i>
                <h5 class="card-title">AI智能生成</h5>
                <p class="card-text">基于ERNIE Bot，智能生成专业测试用例</p>
                <button class="btn btn-success" onclick="showGenerateModal()">
                    <i class="fas fa-wand-magic me-1"></i>
                    开始生成
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-info">
            <div class="card-body text-center">
                <i class="fas fa-file-alt fa-2x text-info mb-3"></i>
                <h5 class="card-title">查看文件</h5>
                <p class="card-text">浏览已生成的测试用例文件</p>
                <a href="{{ url_for('list_files') }}" class="btn btn-info">
                    <i class="fas fa-folder-open me-1"></i>
                    文件列表
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-2x text-warning mb-3"></i>
                <h5 class="card-title">统计分析</h5>
                <p class="card-text">测试用例覆盖率和质量分析</p>
                <button class="btn btn-warning" onclick="showStatsModal()">
                    <i class="fas fa-chart-line me-1"></i>
                    查看统计
                </button>
            </div>
        </div>
    </div>
</div>

<!-- AI生成模态框 -->
<div class="modal fade" id="generateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-magic me-2"></i>AI智能生成测试用例
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="generateForm">
                    <div class="mb-3">
                        <label for="scenarioSelect" class="form-label">选择测试场景</label>
                        <select class="form-select" id="scenarioSelect" required>
                            <option value="">请选择测试场景</option>
                            <option value="邮箱注册">邮箱注册</option>
                            <option value="手机号注册">手机号注册</option>
                            <option value="第三方登录">第三方登录</option>
                            <option value="密码登录">密码登录</option>
                            <option value="站点切换">站点切换</option>
                            <option value="安全机制">安全机制</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="customRequirements" class="form-label">自定义需求（可选）</label>
                        <textarea class="form-control" id="customRequirements" rows="4" 
                                  placeholder="请输入特殊的测试需求或场景..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateTestCases()">
                    <i class="fas fa-magic me-1"></i>
                    生成测试用例
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计模态框 -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar me-2"></i>测试用例统计
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ structure|length }}</h4>
                            <small>主模块</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-success">
                                {% set sub_count = 0 %}
                                {% for module in structure.values() %}
                                    {% if module.get('子模块') %}
                                        {% set sub_count = sub_count + module['子模块']|length %}
                                    {% endif %}
                                {% endfor %}
                                {{ sub_count }}
                            </h4>
                            <small>子模块</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">12</h4>
                            <small>覆盖国家</small>
                        </div>
                    </div>
                </div>
                <hr>
                <h6>模块分布</h6>
                {% for module_name, module_info in structure.items() %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{{ module_info.get('icon', '📁') }} {{ module_name }}</span>
                    <span class="badge bg-primary">
                        {{ module_info.get('子模块', {})|length }} 子模块
                    </span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function showGenerateModal() {
    new bootstrap.Modal(document.getElementById('generateModal')).show();
}

function showStatsModal() {
    new bootstrap.Modal(document.getElementById('statsModal')).show();
}

function generateTestCases() {
    const scenario = document.getElementById('scenarioSelect').value;
    const requirements = document.getElementById('customRequirements').value;
    
    if (!scenario) {
        alert('请选择测试场景');
        return;
    }
    
    // 关闭生成模态框
    bootstrap.Modal.getInstance(document.getElementById('generateModal')).hide();
    
    // 显示加载提示
    showLoading('正在生成测试用例，请稍候...');
    
    // 发送生成请求
    fetch('/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            scenario_name: scenario,
            custom_requirements: requirements
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            alert('测试用例生成成功！\n文件：' + data.filename);
            // 刷新页面或跳转到文件列表
            window.location.href = '/files';
        } else {
            alert('生成失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading();
        alert('请求失败：' + error);
    });
}
</script>
{% endblock %}
