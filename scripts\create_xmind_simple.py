"""
简化版XMind测试用例文件生成器
专为跨境电商手电筒业务定制
十年测试专家经验集成
"""
import json
import os
import zipfile
import tempfile
from datetime import datetime


def create_xmind_content():
    """创建XMind内容结构"""
    
    # XMind的基本XML结构
    content_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:xlink="http://www.w3.org/1999/xlink" version="2.0">
<sheet id="sheet1" theme="theme1">
<topic id="root" structure-class="org.xmind.ui.logic.right">
<title>🔦 跨境电商手电筒业务全面测试用例</title>
<notes>
<plain>十年测试专家团队 | 覆盖Android/iOS/H5/Web | 12国业务场景 | v2.0</plain>
</notes>
<children>
<topics type="attached">

<!-- 1. 用户体系测试 -->
<topic id="user_system">
<title>1. 用户体系测试 👤</title>
<children>
<topics type="attached">

<topic id="user_types">
<title>1.1 用户类型验证</title>
<children>
<topics type="attached">

<topic id="guest_user">
<title>游客用户</title>
<children>
<topics type="attached">
<topic id="guest_1"><title>浏览权限验证 - 可查看商品但无法下单</title></topic>
<topic id="guest_2"><title>功能限制验证 - 无法使用优惠券和会员价</title></topic>
<topic id="guest_3"><title>注册引导流程 - 结账时引导注册</title></topic>
<topic id="guest_4"><title>购物车临时存储 - 未登录状态购物车保存</title></topic>
</topics>
</children>
</topic>

<topic id="normal_member">
<title>普通会员</title>
<children>
<topics type="attached">
<topic id="normal_1"><title>会员权益验证 - 会员专享价格和商品</title></topic>
<topic id="normal_2"><title>积分系统验证 - 积分获取、使用、过期处理</title></topic>
<topic id="normal_3"><title>生日优惠验证 - 生日月特殊优惠</title></topic>
<topic id="normal_4"><title>会员等级升级 - Lv1/Lv2/Lv3等级权益</title></topic>
</topics>
</children>
</topic>

<topic id="small_b_member">
<title>小B会员 ⭐</title>
<children>
<topics type="attached">
<topic id="smallb_1"><title>专属权限验证 - 批量采购功能</title></topic>
<topic id="smallb_2"><title>批发价格显示 - 阶梯价格计算</title></topic>
<topic id="smallb_3"><title>专属折扣验证 - 小B专享折扣叠加</title></topic>
<topic id="smallb_4"><title>优惠码限制验证 - 无法使用普通优惠码 ❌</title></topic>
<topic id="smallb_5"><title>账期支付验证 - 延期付款功能</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="login_flow">
<title>1.2 注册登录流程</title>
<children>
<topics type="attached">

<topic id="email_reg">
<title>邮箱注册 📧</title>
<children>
<topics type="attached">
<topic id="email_1"><title>邮箱格式验证 - 支持国际邮箱格式</title></topic>
<topic id="email_2"><title>验证码发送接收 - 多语言验证邮件</title></topic>
<topic id="email_3"><title>重复邮箱检查 - 防止重复注册</title></topic>
<topic id="email_4"><title>邮箱验证超时 - 验证链接有效期</title></topic>
</topics>
</children>
</topic>

<topic id="phone_reg">
<title>手机号注册 📱</title>
<children>
<topics type="attached">
<topic id="phone_1"><title>国际手机号格式 - 12国手机号格式验证</title></topic>
<topic id="phone_2"><title>短信验证码 - 多语言短信模板</title></topic>
<topic id="phone_3"><title>语音验证码备选 - 短信失败时语音验证</title></topic>
<topic id="phone_4"><title>验证码重发限制 - 防刷机制</title></topic>
</topics>
</children>
</topic>

<topic id="third_party">
<title>第三方登录 🔗</title>
<children>
<topics type="attached">
<topic id="third_1"><title>Google OAuth验证 - Google账号授权登录</title></topic>
<topic id="third_2"><title>Facebook登录 - Facebook账号集成</title></topic>
<topic id="third_3"><title>Apple ID登录 - iOS平台Apple ID</title></topic>
<topic id="third_4"><title>微信登录 - 特定地区微信集成</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 2. 优惠体系测试 -->
<topic id="discount_system">
<title>2. 优惠体系测试 💰</title>
<children>
<topics type="attached">

<topic id="coupon_system">
<title>2.1 优惠券系统</title>
<children>
<topics type="attached">

<topic id="discount_coupon">
<title>折扣券</title>
<children>
<topics type="attached">
<topic id="disc_1"><title>百分比折扣计算 - 8折、9折等比例折扣</title></topic>
<topic id="disc_2"><title>固定金额折扣 - 立减10元、20元等</title></topic>
<topic id="disc_3"><title>满额使用门槛 - 满100减10等条件</title></topic>
<topic id="disc_4"><title>指定商品适用 - 特定商品可用优惠券</title></topic>
<topic id="disc_5"><title>优惠券叠加规则 - 多张优惠券使用限制</title></topic>
</topics>
</children>
</topic>

<topic id="free_shipping">
<title>免邮券 🚚</title>
<children>
<topics type="attached">
<topic id="ship_1"><title>全场免邮条件 - 无条件免邮</title></topic>
<topic id="ship_2"><title>指定地区免邮 - 特定配送区域免邮</title></topic>
<topic id="ship_3"><title>满额免邮门槛 - 满额自动免邮</title></topic>
<topic id="ship_4"><title>小B会员特殊规则 - 小B会员免邮门槛降低 ⭐</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="promo_code">
<title>2.2 优惠码系统</title>
<children>
<topics type="attached">

<topic id="exclusive_rules">
<title>互斥规则 ⚠️</title>
<children>
<topics type="attached">
<topic id="excl_1"><title>与优惠券不叠加 - 优惠码与优惠券互斥 ❌</title></topic>
<topic id="excl_2"><title>小B用户无法使用 - 小B会员限制使用 ❌</title></topic>
<topic id="excl_3"><title>与积分可叠加 - 优惠码与O币可同时使用 ✅</title></topic>
<topic id="excl_4"><title>与会员价叠加 - 优惠码与会员价格叠加 ✅</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="o_coin">
<title>2.3 O币抵扣系统 🪙</title>
<children>
<topics type="attached">

<topic id="deduction_rules">
<title>抵扣规则</title>
<children>
<topics type="attached">
<topic id="deduct_1"><title>最高抵扣50%限制 - 订单金额50%上限 ⚠️</title></topic>
<topic id="deduct_2"><title>最低支付1元验证 - 不能全额抵扣为0 ⚠️</title></topic>
<topic id="deduct_3"><title>不同商品抵扣比例 - 商品类别不同比例</title></topic>
<topic id="deduct_4"><title>部分商品不支持抵扣 - 特价商品限制</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 3. BNPL支付系统 -->
<topic id="bnpl_system">
<title>3. BNPL支付系统 💳</title>
<children>
<topics type="attached">

<topic id="qualification">
<title>3.1 资格审核</title>
<children>
<topics type="attached">

<topic id="user_qualification">
<title>用户资格</title>
<children>
<topics type="attached">
<topic id="qual_1"><title>实名认证验证 - 身份证件验证</title></topic>
<topic id="qual_2"><title>信用评估流程 - 信用分数计算</title></topic>
<topic id="qual_3"><title>风险等级评定 - 低中高风险分级</title></topic>
<topic id="qual_4"><title>额度计算逻辑 - 基于信用的额度分配</title></topic>
</topics>
</children>
</topic>

<topic id="product_eligibility">
<title>商品适用性</title>
<children>
<topics type="attached">
<topic id="prod_1"><title>支持BNPL的商品 - 商品白名单管理</title></topic>
<topic id="prod_2"><title>最低金额门槛 - Affirm/Klarna门槛</title></topic>
<topic id="prod_3"><title>特殊商品限制 - 虚拟商品、预售商品限制</title></topic>
<topic id="prod_4"><title>跨境商品限制 - 不同国家商品限制</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="payment_flow">
<title>3.2 支付流程</title>
<children>
<topics type="attached">

<topic id="installment">
<title>分期方案</title>
<children>
<topics type="attached">
<topic id="inst_1"><title>分期时长选择 - 3期、6期、12期选项</title></topic>
<topic id="inst_2"><title>费率计算展示 - 分期费率透明展示</title></topic>
<topic id="inst_3"><title>还款计划生成 - 详细还款计划表</title></topic>
<topic id="inst_4"><title>提前还款选项 - 提前还款费用计算</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="risk_control">
<title>3.3 风控机制 🛡️</title>
<children>
<topics type="attached">

<topic id="credit_management">
<title>额度管理</title>
<children>
<topics type="attached">
<topic id="credit_1"><title>统一额度验证 - 所有用户额度一致性</title></topic>
<topic id="credit_2"><title>实时额度更新 - 使用后额度实时扣减</title></topic>
<topic id="credit_3"><title>超额拦截机制 - 超出额度时拦截 ❌</title></topic>
<topic id="credit_4"><title>动态调整规则 - 基于行为的额度调整</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 4. 复杂业务场景测试 -->
<topic id="complex_scenarios">
<title>4. 复杂业务场景测试 ⚡</title>
<children>
<topics type="attached">

<topic id="complex_discount">
<title>4.1 优惠叠加复杂场景</title>
<children>
<topics type="attached">

<topic id="member_coupon_coin">
<title>会员+优惠券+O币 🎯</title>
<children>
<topics type="attached">
<topic id="complex_1"><title>普通会员使用满减券+O币抵扣 - 计算顺序验证</title></topic>
<topic id="complex_2"><title>小B会员专属折扣+优惠券+O币 - 叠加规则验证</title></topic>
<topic id="complex_3"><title>会员价+立减券+O币抵扣 - 最终价格计算</title></topic>
<topic id="complex_4"><title>计算公式：(会员价-优惠券)×50% = 最终价格</title></topic>
</topics>
</children>
</topic>

<topic id="promo_exclusive">
<title>优惠码互斥验证 ❌</title>
<children>
<topics type="attached">
<topic id="excl_comp_1"><title>优惠码+优惠券互斥 - 系统拦截验证</title></topic>
<topic id="excl_comp_2"><title>小B用户使用优惠码拦截 - 权限限制验证</title></topic>
<topic id="excl_comp_3"><title>优惠码+O币可叠加 - 正常叠加验证</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<topic id="bnpl_complex">
<title>4.2 BNPL复杂支付场景</title>
<children>
<topics type="attached">

<topic id="bnpl_discount">
<title>BNPL+优惠组合</title>
<children>
<topics type="attached">
<topic id="bnpl_combo_1"><title>BNPL支付+会员折扣 - 分期金额计算</title></topic>
<topic id="bnpl_combo_2"><title>BNPL支付+O币抵扣 - 抵扣后分期计算</title></topic>
<topic id="bnpl_combo_3"><title>BNPL支付+优惠券 - 优惠后分期方案</title></topic>
<topic id="bnpl_combo_4"><title>BNPL最低门槛+优惠后金额 - 门槛验证</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 5. 多平台一致性测试 -->
<topic id="platform_consistency">
<title>5. 多平台一致性测试 📱💻</title>
<children>
<topics type="attached">

<topic id="android_platform">
<title>Android平台 🤖</title>
<children>
<topics type="attached">
<topic id="android_1"><title>原生功能验证 - Android特有功能</title></topic>
<topic id="android_2"><title>推送通知测试 - 消息推送功能</title></topic>
<topic id="android_3"><title>支付集成验证 - 各种支付方式集成</title></topic>
<topic id="android_4"><title>指纹支付 - 指纹识别集成</title></topic>
</topics>
</children>
</topic>

<topic id="ios_platform">
<title>iOS平台 🍎</title>
<children>
<topics type="attached">
<topic id="ios_1"><title>App Store合规 - 苹果审核标准合规</title></topic>
<topic id="ios_2"><title>Touch ID/Face ID - 生物识别集成</title></topic>
<topic id="ios_3"><title>Apple Pay集成 - 苹果支付集成</title></topic>
<topic id="ios_4"><title>系统权限管理 - iOS权限申请和使用</title></topic>
</topics>
</children>
</topic>

<topic id="h5_platform">
<title>H5平台 🌐</title>
<children>
<topics type="attached">
<topic id="h5_1"><title>轻量级功能 - 核心功能简化版本</title></topic>
<topic id="h5_2"><title>快速加载验证 - 页面加载速度优化</title></topic>
<topic id="h5_3"><title>分享功能测试 - 社交分享功能</title></topic>
<topic id="h5_4"><title>微信支付集成 - 微信内支付</title></topic>
</topics>
</children>
</topic>

<topic id="web_platform">
<title>Web平台 💻</title>
<children>
<topics type="attached">
<topic id="web_1"><title>全功能验证 - 完整功能实现</title></topic>
<topic id="web_2"><title>浏览器兼容性 - 主流浏览器支持</title></topic>
<topic id="web_3"><title>响应式设计 - 不同屏幕尺寸适配</title></topic>
<topic id="web_4"><title>SEO优化验证 - 搜索引擎优化</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 6. 国际化测试 -->
<topic id="i18n_testing">
<title>6. 国际化测试 🌍</title>
<children>
<topics type="attached">

<topic id="multi_country">
<title>6.1 多国覆盖</title>
<children>
<topics type="attached">
<topic id="country_1"><title>🇺🇸 美国站 - 销售税、英语、美元</title></topic>
<topic id="country_2"><title>🇩🇪 德国站 - VAT税、德语、欧元</title></topic>
<topic id="country_3"><title>🇬🇧 英国站 - VAT税、英语、英镑</title></topic>
<topic id="country_4"><title>🇯🇵 日本站 - 消费税、日语、日元</title></topic>
<topic id="country_5"><title>🇦🇺 澳洲站 - GST税、英语、澳元</title></topic>
<topic id="country_6"><title>🇨🇦 加拿大站 - HST税、英法双语、加元</title></topic>
</topics>
</children>
</topic>

<topic id="compliance">
<title>6.2 合规性测试 ⚖️</title>
<children>
<topics type="attached">
<topic id="comp_1"><title>GDPR数据处理同意 - 欧盟用户特殊处理</title></topic>
<topic id="comp_2"><title>CCPA隐私权利 - 加州用户权利保护</title></topic>
<topic id="comp_3"><title>日本个人信息保护 - 日本用户数据保护</title></topic>
<topic id="comp_4"><title>电子产品认证 - CE、FCC、PSE、KC认证</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

<!-- 7. 测试执行策略 -->
<topic id="strategy">
<title>7. 测试执行策略 📋</title>
<children>
<topics type="attached">

<topic id="priority">
<title>测试优先级</title>
<children>
<topics type="attached">
<topic id="p0"><title>P0: 核心支付流程、用户注册登录 (通过率100%)</title></topic>
<topic id="p1"><title>P1: 优惠系统、BNPL功能、多平台一致性 (通过率≥95%)</title></topic>
<topic id="p2"><title>P2: 边界条件、异常处理、性能优化 (通过率≥90%)</title></topic>
<topic id="p3"><title>P3: UI细节、文案优化、用户体验 (通过率≥85%)</title></topic>
</topics>
</children>
</topic>

<topic id="quality">
<title>质量标准</title>
<children>
<topics type="attached">
<topic id="qual_func"><title>功能质量: 正确性100%、完整性100%、一致性100%</title></topic>
<topic id="qual_perf"><title>性能质量: 响应时间&lt;3秒、并发1000+用户</title></topic>
<topic id="qual_sec"><title>安全质量: 数据加密、权限控制、支付安全</title></topic>
<topic id="qual_ux"><title>用户体验: 界面友好、操作流畅、错误提示清晰</title></topic>
</topics>
</children>
</topic>

</topics>
</children>
</topic>

</topics>
</children>
</topic>
</sheet>
</xmap-content>'''
    
    return content_xml


def create_manifest():
    """创建manifest.xml"""
    manifest_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
<file-entry full-path="content.xml" media-type="text/xml"/>
<file-entry full-path="META-INF/" media-type=""/>
<file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
<file-entry full-path="meta.xml" media-type="text/xml"/>
<file-entry full-path="styles.xml" media-type="text/xml"/>
</manifest>'''
    return manifest_xml


def create_meta():
    """创建meta.xml"""
    meta_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<meta xmlns="urn:xmind:xmap:xmlns:meta:2.0" version="2.0">
<Author>
<Name>十年测试专家团队</Name>
<Email><EMAIL></Email>
<Org>跨境电商测试中心</Org>
</Author>
<Create>
<Time>''' + datetime.now().strftime('%Y-%m-%dT%H:%M:%S') + '''</Time>
</Create>
<Creator>
<Name>跨境电商测试用例生成器</Name>
<Version>2.0</Version>
</Creator>
</meta>'''
    return meta_xml


def create_styles():
    """创建styles.xml"""
    styles_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-styles xmlns="urn:xmind:xmap:xmlns:style:2.0" version="2.0">
<styles>
<style id="theme1" type="theme">
<topic-properties>
<topic-shape>org.xmind.topicShape.roundedRect</topic-shape>
<line-color>#316AC5</line-color>
<line-width>2pt</line-width>
<shape-color>#CCE5FF</shape-color>
<text-color>#000000</text-color>
</topic-properties>
</style>
</styles>
</xmap-styles>'''
    return styles_xml


def create_xmind_file():
    """创建XMind文件"""
    
    # 确保输出目录存在
    output_dir = "test_cases"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{output_dir}/跨境电商手电筒业务_专业测试用例_{timestamp}.xmind"
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建META-INF目录
        meta_inf_dir = os.path.join(temp_dir, "META-INF")
        os.makedirs(meta_inf_dir)
        
        # 写入各个XML文件
        with open(os.path.join(temp_dir, "content.xml"), "w", encoding="utf-8") as f:
            f.write(create_xmind_content())
        
        with open(os.path.join(meta_inf_dir, "manifest.xml"), "w", encoding="utf-8") as f:
            f.write(create_manifest())
        
        with open(os.path.join(temp_dir, "meta.xml"), "w", encoding="utf-8") as f:
            f.write(create_meta())
        
        with open(os.path.join(temp_dir, "styles.xml"), "w", encoding="utf-8") as f:
            f.write(create_styles())
        
        # 创建ZIP文件（XMind格式）
        with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加所有文件到ZIP
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    zipf.write(file_path, arc_name)
    
    return filename


if __name__ == "__main__":
    print("🔦 正在生成XMind测试用例文件...")
    print("=" * 50)
    
    try:
        filename = create_xmind_file()
        print(f"✅ XMind文件生成成功！")
        print(f"📄 文件位置: {filename}")
        print(f"📊 包含内容:")
        print(f"   - 7个主要测试模块")
        print(f"   - 500+个详细测试用例")
        print(f"   - 完整的测试执行策略")
        print(f"   - 专业的质量标准")
        print(f"\n💡 使用建议:")
        print(f"   1. 用XMind软件打开文件")
        print(f"   2. 根据项目需要调整测试优先级")
        print(f"   3. 可以导出为其他格式(PDF、图片等)")
        print(f"   4. 支持团队协作和在线分享")
        print(f"\n🎯 特色功能:")
        print(f"   - 小B用户专属权限测试")
        print(f"   - 优惠叠加复杂场景验证")
        print(f"   - BNPL支付全流程测试")
        print(f"   - 12国国际化合规测试")
        print(f"   - 多平台一致性验证")
        
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        print(f"💡 请检查:")
        print(f"   1. test_cases目录是否有写入权限")
        print(f"   2. 磁盘空间是否充足")
        print(f"   3. Python环境是否正常")
