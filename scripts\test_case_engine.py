"""
专业测试用例生成引擎
十年测试专家经验，专为跨境电商手电筒业务定制
"""
import json
import os
import sys
import erniebot
from datetime import datetime
from typing import List, Dict, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config


class TestCaseEngine:
    """专业测试用例生成引擎"""
    
    def __init__(self):
        """初始化引擎"""
        Config.validate()
        
        # 配置ERNIE Bot
        erniebot.api_type = Config.ERNIE_API_TYPE
        erniebot.access_token = Config.ERNIE_ACCESS_TOKEN
        
        # 加载知识库
        self.knowledge_base = self._load_knowledge_base()
        self.test_structure = self._load_test_structure()
        self.test_templates = self._load_test_templates()
        
        print("🔧 测试用例生成引擎初始化完成")
        print(f"📚 已加载 {len(self.knowledge_base)} 个知识库模块")
        print(f"🏗️ 已加载测试结构：{len(self.test_structure.get('测试用例分层结构', {}))} 个主模块")
    
    def _load_knowledge_base(self) -> Dict:
        """加载知识库"""
        knowledge = {}
        kb_dir = "knowledge_base"
        
        for filename in os.listdir(kb_dir):
            if filename.endswith('.json') and filename != 'test_case_structure.json' and filename != 'test_case_templates.json':
                with open(os.path.join(kb_dir, filename), 'r', encoding='utf-8') as f:
                    key = filename.replace('.json', '')
                    knowledge[key] = json.load(f)
        
        return knowledge
    
    def _load_test_structure(self) -> Dict:
        """加载测试用例结构"""
        try:
            with open('knowledge_base/test_case_structure.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def _load_test_templates(self) -> Dict:
        """加载测试用例模板"""
        try:
            with open('knowledge_base/test_case_templates.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def get_test_structure(self) -> Dict:
        """获取测试用例分层结构"""
        return self.test_structure.get('测试用例分层结构', {})
    
    def get_module_scenarios(self, module_path: List[str]) -> Dict:
        """
        获取指定模块的测试场景
        
        Args:
            module_path: 模块路径，如 ['用户管理', '用户注册']
        
        Returns:
            测试场景字典
        """
        structure = self.get_test_structure()
        
        # 导航到指定模块
        current = structure
        for path_item in module_path:
            if path_item in current:
                current = current[path_item]
            else:
                return {}
        
        # 返回测试场景
        if '测试场景' in current:
            return current['测试场景']
        elif '子模块' in current:
            return current['子模块']
        else:
            return current
    
    def get_test_cases_for_scenario(self, scenario_name: str) -> List[Dict]:
        """
        获取指定场景的测试用例模板
        
        Args:
            scenario_name: 场景名称，如 '邮箱注册'
        
        Returns:
            测试用例列表
        """
        templates = self.test_templates.get('测试用例模板', {})
        return templates.get(scenario_name, [])
    
    def generate_test_cases_by_scenario(self, scenario_name: str, custom_requirements: str = "") -> str:
        """
        根据场景生成测试用例
        
        Args:
            scenario_name: 场景名称
            custom_requirements: 自定义需求
        
        Returns:
            生成的测试用例
        """
        # 获取场景模板
        templates = self.get_test_cases_for_scenario(scenario_name)
        
        # 构建专业提示词
        prompt = self._build_professional_prompt(scenario_name, templates, custom_requirements)
        
        try:
            response = erniebot.ChatCompletion.create(
                model=Config.ERNIE_MODEL,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            return response.get_result()
        except Exception as e:
            return f"生成失败：{str(e)}"
    
    def _build_professional_prompt(self, scenario_name: str, templates: List[Dict], custom_requirements: str) -> str:
        """构建专业的提示词"""
        
        # 基础上下文
        context = f"""
你是一名拥有十年经验的资深测试专家，专精于跨境电商和电子产品测试。
你正在为手电筒跨境电商平台的【{scenario_name}】功能设计测试用例。

## 业务背景
- 产品：专业手电筒跨境电商平台
- 覆盖市场：美国、德国、奥地利、英国、澳大利亚、意大利、加拿大、西班牙、法国、日本、韩国
- 用户群体：户外爱好者、专业救援队、安保人员、军警、工业用户、普通消费者
- 特殊要求：电子产品认证、电池运输限制、多国合规

## 专业知识库
"""
        
        # 添加相关知识库
        for kb_name, kb_content in self.knowledge_base.items():
            context += f"\n### {kb_name}:\n{json.dumps(kb_content, ensure_ascii=False, indent=2)}\n"
        
        # 添加模板参考
        if templates:
            context += f"\n## 参考模板（{scenario_name}）\n"
            for template in templates[:2]:  # 只显示前2个模板作为参考
                context += f"### {template.get('用例标题', '')}\n"
                context += f"- 用例编号：{template.get('用例编号', '')}\n"
                context += f"- 优先级：{template.get('优先级', '')}\n"
                context += f"- 测试步骤：{template.get('测试步骤', [])}\n"
                context += f"- 预期结果：{template.get('预期结果', [])}\n\n"
        
        # 任务要求
        task = f"""
## 任务要求
请为【{scenario_name}】功能生成专业的测试用例，要求：

### 输出格式
使用表格格式，包含以下字段：
| 用例编号 | 用例标题 | 测试模块 | 优先级 | 前置条件 | 测试步骤 | 预期结果 | 测试数据 | 备注 |

### 测试覆盖要求
1. **正向测试**：正常业务流程，确保功能正确实现
2. **边界值测试**：输入长度限制、格式要求、数值范围
3. **异常测试**：错误输入、网络异常、系统故障
4. **兼容性测试**：多浏览器、移动端、不同操作系统
5. **安全测试**：输入验证、权限控制、数据保护
6. **国际化测试**：多语言、多货币、多时区
7. **合规性测试**：GDPR、CCPA、电子产品认证

### 跨境电商特殊场景
- 12个国家站点的差异化测试
- 多语言界面和错误提示
- 多货币价格显示和计算
- 各国法规合规性验证
- 电子产品认证标识
- 电池运输限制检查

### 手电筒行业特色
- 专业用户群体（救援队、军警、安保）
- 技术参数国际化（亮度单位、防水等级）
- 产品认证要求（CE、FCC、PSE、KC等）
- 电池安全和运输限制

### 用例编号规则
- 格式：TC_[模块简称]_[场景简称]_[序号]
- 示例：TC_REG_EMAIL_001（注册_邮箱_001）

### 优先级定义
- 高：核心功能，影响主要业务流程
- 中：重要功能，影响用户体验
- 低：辅助功能，优化类需求

"""
        
        # 自定义需求
        if custom_requirements:
            task += f"\n### 特殊需求\n{custom_requirements}\n"
        
        task += """
请确保测试用例具有：
- 可执行性：步骤清晰，易于执行
- 完整性：覆盖所有重要场景
- 专业性：体现十年测试经验
- 实用性：贴近真实业务场景
"""
        
        return context + task
    
    def save_test_cases(self, test_cases: str, filename: str = None) -> str:
        """保存测试用例"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_cases_{timestamp}.txt"
        
        filepath = os.path.join("test_cases", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(test_cases)
        
        return filepath


# 使用示例
if __name__ == "__main__":
    engine = TestCaseEngine()
    
    # 查看测试结构
    structure = engine.get_test_structure()
    print("📋 测试用例结构：")
    for module_name, module_info in structure.items():
        print(f"  {module_info.get('icon', '📁')} {module_name}: {module_info.get('description', '')}")
    
    # 生成邮箱注册测试用例
    print("\n🚀 生成邮箱注册测试用例...")
    test_cases = engine.generate_test_cases_by_scenario("邮箱注册")
    print(test_cases[:500] + "..." if len(test_cases) > 500 else test_cases)
    
    # 保存测试用例
    filepath = engine.save_test_cases(test_cases, "邮箱注册测试用例.txt")
    print(f"\n💾 测试用例已保存：{filepath}")
