# 🌍 跨境电商测试用例生成器 - 使用说明

## 📋 项目概述

这是一个基于ERNIE Bot SDK开发的智能测试用例生成工具，专门为跨境电商业务定制。系统能够根据需求描述自动生成高质量的测试用例，覆盖多站点、多语言、多货币等复杂场景。

## 🚀 快速开始

### 1️⃣ 获取Access Token
- 访问：https://aistudio.baidu.com/index/accessToken
- 登录百度账号，获取免费的Access Token
- 每个账户有100万Token免费额度

### 2️⃣ 配置环境
```bash
# 编辑.env文件
ERNIE_ACCESS_TOKEN=你的真实Token
```

### 3️⃣ 安装依赖
```bash
pip install -r requirements.txt
```

### 4️⃣ 启动系统
```bash
# 方式1：交互式菜单
python start.py

# 方式2：直接启动Web界面
python start.py --web

# 方式3：运行示例
python start.py --examples

# 方式4：离线演示（无需Token）
python demo_offline.py
```

## 🎯 核心功能

### 📝 智能测试用例生成
- **输入**：业务需求描述
- **输出**：结构化测试用例表格
- **特色**：专门针对跨境电商场景优化

### 🌐 跨境电商专业知识库
- **多站点规则**：美国、欧洲、日本、澳洲
- **支付方式**：信用卡、数字钱包、本地支付
- **合规要求**：GDPR、VAT、产品认证
- **物流规则**：跨境物流、清关、配送

### 🔄 多轮对话优化
- 支持持续优化测试用例
- 可以补充特定测试场景
- 保存完整对话历史

### 📊 多格式导出
- **Excel**：便于测试管理
- **CSV**：兼容性好
- **JSON**：程序化处理
- **XMind**：思维导图格式

## 📚 使用示例

### 注册登录功能需求
```
跨境电商用户注册登录功能：
1. 支持邮箱和手机号注册
2. 多语言界面（中英日德）
3. 多站点选择（美国、欧洲、日本）
4. 社交登录（Google、Facebook）
5. 密码强度要求
6. GDPR合规
7. 异地登录检测
8. 双因素认证
```

### 生成的测试用例示例
| 用例编号 | 测试模块 | 测试场景 | 操作步骤 | 预期结果 | 优先级 |
|----------|----------|----------|----------|----------|--------|
| TC001 | 用户注册 | 邮箱注册-美国站 | 1.选择美国站<br>2.输入邮箱<br>3.设置密码 | 注册成功，发送验证邮件 | 高 |
| TC002 | 用户注册 | GDPR合规提示 | 1.欧洲用户注册<br>2.查看隐私政策 | 显示GDPR合规提示 | 高 |

## 🛠️ 高级功能

### 命令行使用
```bash
# 生成测试用例并导出Excel
python cli_tool.py --requirement "需求描述" --export excel

# 优化测试用例
python cli_tool.py --requirement "需求" --optimize "增加安全测试" --export excel
```

### Python脚本集成
```python
from test_case_generator import CrossBorderTestGenerator

generator = CrossBorderTestGenerator()
test_cases = generator.generate_test_cases(
    requirement="需求描述",
    test_type="功能测试",
    priority="高"
)
excel_file = generator.export_test_cases(test_cases, "excel")
```

## 📁 项目结构
```
├── config.py              # 配置文件
├── test_case_generator.py  # 核心生成器
├── web_interface.py        # Web界面
├── cli_tool.py            # 命令行工具
├── knowledge_base/        # 业务知识库
├── examples/              # 示例代码
├── output/               # 输出文件
└── start.py              # 启动脚本
```

## 🎨 Web界面功能

### 配置区域
- Access Token设置
- 生成器初始化

### 需求输入
- 详细需求描述
- 测试类型选择
- 优先级过滤
- 输出格式选择

### 高级功能
- 测试用例优化
- 多格式导出
- 对话历史管理

## 💡 使用技巧

### 1. 需求描述要点
- 详细描述功能点
- 明确业务规则
- 指出特殊场景
- 包含合规要求

### 2. 优化建议
- "增加边界值测试"
- "补充安全性测试"
- "添加性能测试场景"
- "增加移动端兼容性测试"

### 3. 最佳实践
- 从简单功能开始
- 逐步增加复杂度
- 利用多轮对话优化
- 定期导出备份

## ❓ 常见问题

### Q: Token获取失败？
A: 确保百度账号已登录，访问AI Studio个人中心获取

### Q: 生成质量不理想？
A: 1) 提供更详细的需求描述 2) 使用多轮对话优化 3) 结合业务知识库

### Q: 如何自定义业务规则？
A: 编辑knowledge_base/目录下的JSON文件，添加特定规则

### Q: 支持哪些导出格式？
A: Excel、CSV、JSON、XMind格式，满足不同使用场景

## 🔧 技术支持

- **文档**：README.md、QUICK_START.md
- **示例**：examples/cross_border_examples.py
- **演示**：python demo_offline.py

---

🌟 **专为跨境电商测试而生，让测试用例编写更智能、更高效！**
