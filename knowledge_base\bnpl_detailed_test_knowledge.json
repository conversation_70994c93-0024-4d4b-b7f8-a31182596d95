{"bnpl_detailed_test_knowledge": {"system_overview": {"name": "BNPL先享后付系统", "description": "Buy Now Pay Later - 先享后付金融服务系统", "core_features": ["用户资格审核", "信用评估", "额度管理", "分期支付", "风控机制", "多端支持"], "supported_platforms": ["Android", "iOS", "H5", "Web"], "target_users": ["普通会员", "小B会员", "游客用户"]}, "user_qualification_rules": {"identity_verification": {"required_documents": ["身份证正面", "身份证反面", "人脸识别", "银行卡验证"], "verification_standards": {"id_card_clarity": "图片清晰度 >= 300DPI", "id_card_validity": "证件在有效期内", "face_similarity": "人脸匹配度 >= 95%", "liveness_detection": "活体检测通过", "bank_card_status": "银行卡状态正常"}, "rejection_criteria": ["证件模糊不清", "证件过期", "人脸匹配度低", "活体检测失败", "银行卡冻结或过期"]}, "credit_assessment": {"scoring_factors": {"identity_verification": {"weight": 20, "description": "身份认证完整性"}, "purchase_history": {"weight": 25, "description": "历史购买记录"}, "payment_behavior": {"weight": 30, "description": "还款行为表现"}, "third_party_credit": {"weight": 15, "description": "第三方信用数据"}, "device_risk": {"weight": 10, "description": "设备和行为风险"}}, "risk_levels": {"low_risk": {"score_range": "800-1000", "characteristics": "信用优秀，历史良好", "default_limit": "5000-20000"}, "medium_risk": {"score_range": "600-799", "characteristics": "信用一般，部分风险点", "default_limit": "1000-5000"}, "high_risk": {"score_range": "300-599", "characteristics": "信用较差，多个风险点", "default_limit": "500-1000"}, "reject": {"score_range": "0-299", "characteristics": "信用极差，直接拒绝", "default_limit": "0"}}}, "credit_limit_calculation": {"base_limits": {"new_user": 500, "verified_user": 1000, "bank_verified": 2000, "income_verified": 5000, "asset_verified": 10000}, "adjustment_factors": {"age_factor": {"18-25": 0.8, "26-35": 1.0, "36-45": 1.2, "46-55": 1.0, "56+": 0.8}, "education_factor": {"高中及以下": 0.9, "大专": 1.0, "本科": 1.1, "硕士": 1.2, "博士": 1.3}, "occupation_factor": {"学生": 0.8, "企业员工": 1.0, "公务员": 1.2, "自由职业": 0.9, "企业主": 1.1}}, "dynamic_adjustments": {"good_payment": {"condition": "连续3期按时还款", "adjustment": "+20%"}, "excellent_payment": {"condition": "连续6期按时还款", "adjustment": "+50%"}, "late_payment": {"condition": "逾期1-7天", "adjustment": "-10%"}, "serious_overdue": {"condition": "逾期超过30天", "adjustment": "-50%"}}}}, "product_eligibility_rules": {"supported_categories": ["电子产品", "户外装备", "工具设备", "配件类", "套装组合", "品牌商品"], "restricted_categories": ["虚拟商品", "易腐商品", "定制商品", "危险品", "处方商品", "成人用品", "博彩相关", "违法商品"], "price_thresholds": {"minimum_amount": {"USD": 50, "CNY": 300, "EUR": 45, "GBP": 40}, "maximum_amount": {"USD": 5000, "CNY": 35000, "EUR": 4500, "GBP": 4000}}, "inventory_requirements": {"in_stock": "正常支持BNPL", "low_stock": "库存<10件时限制BNPL", "out_of_stock": "缺货时禁用BNPL", "pre_order": "预售商品特殊处理"}}, "multiplatform_support": {"android": {"supported_versions": "Android 6.0+", "biometric_auth": ["指纹识别", "面部解锁", "图案解锁", "PIN码"], "special_features": ["原生推送通知", "后台状态同步", "设备指纹识别", "权限管理"]}, "ios": {"supported_versions": "iOS 12.0+", "biometric_auth": ["Touch ID", "Face ID", "密码回退"], "special_features": ["Apple Pay集成", "Wallet集成", "App Store合规", "隐私保护"]}, "h5": {"browser_support": ["Chrome 70+", "Safari 12+", "Firefox 65+", "Edge 79+", "微信浏览器", "QQ浏览器"], "responsive_design": ["手机端适配", "平板端适配", "桌面端适配", "横竖屏切换"]}, "web": {"security_features": ["HTTPS加密", "CSRF防护", "XSS防护", "SQL注入防护", "内容安全策略"], "performance_requirements": {"page_load_time": "< 3秒", "api_response_time": "< 1秒", "concurrent_users": "1000+", "uptime": "99.9%"}}}, "user_type_specific_rules": {"normal_member": {"eligibility": {"registration_required": true, "identity_verification": true, "minimum_age": 18}, "benefits": {"installment_options": ["3期", "6期", "12期"], "fee_discount": "会员专享手续费折扣", "early_repayment": "提前还款手续费减免", "grace_period": "3天宽限期", "customer_service": "专属客服通道"}, "limits": {"base_limit": "500-5000元", "max_limit": "20000元", "daily_limit": "单日最高3000元", "monthly_limit": "单月最高10000元"}}, "small_b_member": {"qualification_requirements": ["营业执照验证", "税务登记证明", "对公银行账户", "经营地址验证", "法人身份验证"], "enhanced_benefits": {"higher_limits": "更高BNPL额度", "bulk_purchase": "支持批量采购", "extended_terms": "更长还款期限", "flexible_payment": "灵活还款方式", "account_period": "30/60/90天账期"}, "special_terms": {"minimum_order": "单笔订单≥1000元", "bulk_discount": "大额订单手续费折扣", "credit_period": "支持账期支付", "invoice_payment": "基于发票延期支付"}}, "guest_user": {"restrictions": ["不显示BNPL选项", "结账时提示注册", "无法使用分期功能"], "conversion_incentives": ["注册即享BNPL优惠", "快速注册通道", "社交登录支持", "手机邮箱快速验证"]}}, "test_execution_strategy": {"priority_levels": {"P0": {"description": "核心功能", "scenarios": ["用户注册登录", "身份验证流程", "基础支付流程", "额度计算"], "pass_rate_requirement": "100%"}, "P1": {"description": "重要功能", "scenarios": ["多端一致性", "风控机制", "优惠组合", "异常处理"], "pass_rate_requirement": "≥95%"}, "P2": {"description": "辅助功能", "scenarios": ["UI细节", "性能优化", "用户体验", "边界条件"], "pass_rate_requirement": "≥90%"}}, "test_environment": {"devices": ["Android高中低端设备各2台", "iPhone新旧机型各2台", "iPad平板设备1台", "PC桌面环境2套"], "browsers": ["Chrome最新版", "Safari最新版", "Firefox最新版", "Edge最新版", "微信浏览器", "QQ浏览器"], "network_conditions": ["WiFi网络", "4G网络", "3G网络", "弱网环境", "网络中断"]}}, "quality_standards": {"functional_quality": {"correctness": "功能实现100%正确", "completeness": "需求覆盖100%完整", "consistency": "多端行为100%一致"}, "performance_quality": {"response_time": "API响应时间<1秒", "page_load": "页面加载时间<3秒", "concurrent_support": "支持1000+并发用户", "availability": "系统可用性99.9%"}, "security_quality": {"data_encryption": "敏感数据全程加密", "access_control": "严格权限控制", "audit_logging": "完整操作审计", "compliance": "符合金融监管要求"}, "user_experience": {"interface_friendly": "界面友好易用", "operation_smooth": "操作流畅自然", "error_handling": "错误提示清晰", "help_support": "帮助支持完善"}}}}