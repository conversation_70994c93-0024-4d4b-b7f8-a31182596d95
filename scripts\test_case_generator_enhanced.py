"""
增强版测试用例生成器
专为跨境电商手电筒业务定制，支持复杂业务场景
十年测试专家经验集成
"""
import json
import os
import sys
import pandas as pd
from datetime import datetime
import erniebot
from typing import List, Dict, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import Config


class EnhancedTestCaseGenerator:
    """增强版测试用例生成器"""
    
    def __init__(self):
        """初始化生成器"""
        Config.validate()
        
        # 配置ERNIE Bot
        erniebot.api_type = Config.ERNIE_API_TYPE
        erniebot.access_token = Config.ERNIE_ACCESS_TOKEN
        
        # 加载知识库
        self.membership_rules = self._load_membership_rules()
        self.test_structure = self._load_test_structure()
        
        print("🔧 增强版测试用例生成器初始化完成")
        print(f"📚 已加载会员体系规则")
        print(f"🏗️ 已加载测试结构框架")
    
    def _load_membership_rules(self) -> Dict:
        """加载会员体系和优惠规则"""
        try:
            with open('knowledge_base/membership_and_discount_rules.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def _load_test_structure(self) -> Dict:
        """加载测试用例结构"""
        try:
            with open('knowledge_base/test_case_structure.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def generate_complex_scenario_tests(self, scenario_type: str) -> str:
        """
        生成复杂业务场景测试用例
        
        Args:
            scenario_type: 场景类型 (membership, discount, bnpl, platform, i18n)
        
        Returns:
            生成的测试用例
        """
        prompt = self._build_complex_scenario_prompt(scenario_type)
        
        try:
            response = erniebot.ChatCompletion.create(
                model=Config.ERNIE_MODEL,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            return response.get_result()
        except Exception as e:
            return f"生成失败：{str(e)}"
    
    def _build_complex_scenario_prompt(self, scenario_type: str) -> str:
        """构建复杂场景提示词"""
        
        base_context = """
你是一名拥有十年经验的资深测试专家，专精于跨境电商复杂业务场景测试。
你正在为手电筒跨境电商平台设计复杂业务场景的测试用例。

## 业务背景
- 产品：专业手电筒跨境电商平台
- 用户类型：游客、普通会员、小B会员
- 优惠体系：优惠券、优惠码、O币抵扣
- 支付方式：传统支付、BNPL先享后付
- 平台覆盖：Android、iOS、H5、Web
- 国际化：12个国家，多语言多货币

## 核心业务规则
"""
        
        # 添加会员体系规则
        if self.membership_rules:
            base_context += f"\n### 会员体系规则:\n{json.dumps(self.membership_rules, ensure_ascii=False, indent=2)}\n"
        
        scenario_prompts = {
            "membership": """
## 测试场景：会员体系复杂场景
请生成以下复杂会员场景的测试用例：

### 重点测试场景
1. **小B用户权限边界测试**
   - 小B用户无法使用优惠码但可使用专属折扣
   - 批发价格计算和阶梯折扣
   - 专属客服渠道验证

2. **会员等级权益差异**
   - Lv1/Lv2/Lv3会员不同折扣
   - 专属商品访问权限
   - 积分返还比例差异

3. **会员升级降级场景**
   - 会员等级变化时权益变化
   - 历史订单权益保持
   - 升级后立即生效验证
""",
            
            "discount": """
## 测试场景：优惠叠加复杂场景
请生成以下复杂优惠叠加的测试用例：

### 重点测试场景
1. **多重优惠叠加计算**
   - 会员价 + 优惠券 + O币抵扣
   - 小B专属折扣 + 优惠券 + O币
   - 生日优惠 + 会员折扣 + O币

2. **优惠互斥规则验证**
   - 优惠码与优惠券不可叠加
   - 小B用户优惠码使用限制
   - 特殊活动期间规则变化

3. **O币抵扣边界测试**
   - 50%抵扣上限验证
   - 最低支付1元限制
   - 不同商品抵扣比例差异
""",
            
            "bnpl": """
## 测试场景：BNPL支付复杂场景
请生成以下BNPL复杂支付的测试用例：

### 重点测试场景
1. **BNPL资格审核**
   - 实名认证流程
   - 信用评估机制
   - 风险等级评定

2. **BNPL与优惠组合**
   - BNPL + 会员折扣
   - BNPL + O币抵扣
   - BNPL + 优惠券叠加

3. **BNPL风控机制**
   - 额度不足拦截
   - 异常行为检测
   - 逾期处理流程
""",
            
            "platform": """
## 测试场景：多平台一致性复杂场景
请生成以下多平台复杂场景的测试用例：

### 重点测试场景
1. **跨平台数据同步**
   - 购物车跨平台同步
   - 订单状态实时同步
   - 用户偏好设置同步

2. **平台特有功能**
   - iOS Face ID支付
   - Android指纹识别
   - H5微信支付集成

3. **平台性能差异**
   - 不同平台加载速度
   - 网络环境适配
   - 内存使用优化
""",
            
            "i18n": """
## 测试场景：国际化复杂场景
请生成以下国际化复杂场景的测试用例：

### 重点测试场景
1. **多国合规差异**
   - GDPR数据处理同意
   - CCPA隐私权利保护
   - 各国电商法规合规

2. **多语言复杂处理**
   - RTL语言布局适配
   - 繁简体中文切换
   - 多语言字符编码

3. **多货币复杂计算**
   - 实时汇率更新
   - 汇率锁定机制
   - 税费多货币计算
"""
        }
        
        task_requirements = """
## 输出要求
请按以下格式生成测试用例：

### 测试用例格式
```
TC_[模块]_[场景]_[序号]: [用例标题]
- 测试目标: [明确的测试目标]
- 前置条件: [执行前需要满足的条件]
- 测试步骤: 
  1. [具体操作步骤]
  2. [具体操作步骤]
  ...
- 预期结果: [期望的测试结果]
- 测试数据: [需要的测试数据]
- 优先级: [P0/P1/P2]
- 备注: [特殊说明或注意事项]
```

### 质量要求
1. **专业性**: 体现十年测试经验的专业水准
2. **完整性**: 覆盖所有关键测试点
3. **可执行性**: 步骤清晰，易于执行
4. **边界性**: 包含边界条件和异常场景
5. **实用性**: 贴近真实业务场景

请确保测试用例具有很强的实战价值，能够发现真实的业务问题。
"""
        
        return base_context + scenario_prompts.get(scenario_type, "") + task_requirements
    
    def generate_xmind_structure(self) -> Dict:
        """生成XMind结构数据"""
        try:
            with open('跨境电商手电筒业务_XMind测试用例结构.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def export_to_excel(self, test_cases: str, filename: str = None) -> str:
        """导出测试用例到Excel"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"跨境电商手电筒_测试用例_{timestamp}.xlsx"
        
        # 解析测试用例文本（简化处理）
        lines = test_cases.split('\n')
        test_case_data = []
        
        current_case = {}
        for line in lines:
            line = line.strip()
            if line.startswith('TC_'):
                if current_case:
                    test_case_data.append(current_case)
                current_case = {'用例编号': line.split(':')[0], '用例标题': line.split(':', 1)[1].strip()}
            elif line.startswith('- 测试目标:'):
                current_case['测试目标'] = line.replace('- 测试目标:', '').strip()
            elif line.startswith('- 前置条件:'):
                current_case['前置条件'] = line.replace('- 前置条件:', '').strip()
            elif line.startswith('- 预期结果:'):
                current_case['预期结果'] = line.replace('- 预期结果:', '').strip()
            elif line.startswith('- 优先级:'):
                current_case['优先级'] = line.replace('- 优先级:', '').strip()
        
        if current_case:
            test_case_data.append(current_case)
        
        # 创建DataFrame并导出
        if test_case_data:
            df = pd.DataFrame(test_case_data)
            filepath = os.path.join("test_cases", filename)
            df.to_excel(filepath, index=False, engine='openpyxl')
            return filepath
        else:
            return "无有效测试用例数据"
    
    def generate_test_report(self, scenario_type: str) -> str:
        """生成测试报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""
# 跨境电商手电筒业务测试报告

## 基本信息
- **生成时间**: {timestamp}
- **测试场景**: {scenario_type}
- **测试专家**: 十年经验测试团队
- **系统版本**: v2.0

## 测试覆盖范围
- **平台覆盖**: Android、iOS、H5、Web
- **国家覆盖**: 美国、德国、奥地利、英国、澳大利亚、意大利、加拿大、西班牙、法国、日本、韩国
- **用户类型**: 游客、普通会员、小B会员
- **业务模块**: 用户体系、优惠系统、BNPL支付、多平台一致性、国际化

## 测试执行建议
1. **优先级执行**: 按P0→P1→P2顺序执行
2. **环境准备**: 确保测试环境数据完整
3. **并行执行**: 不同模块可并行测试
4. **回归验证**: 缺陷修复后及时回归

## 质量标准
- **P0用例通过率**: 100%
- **P1用例通过率**: ≥95%
- **关键缺陷**: 0个
- **性能指标**: 页面加载<3秒

---
*报告由十年测试专家团队生成*
"""
        return report


def main():
    """主函数"""
    print("🔦 跨境电商手电筒业务 - 增强版测试用例生成器")
    print("=" * 60)
    
    generator = EnhancedTestCaseGenerator()
    
    # 生成不同场景的测试用例
    scenarios = {
        "membership": "会员体系复杂场景",
        "discount": "优惠叠加复杂场景", 
        "bnpl": "BNPL支付复杂场景",
        "platform": "多平台一致性场景",
        "i18n": "国际化复杂场景"
    }
    
    print("\n🚀 开始生成复杂业务场景测试用例...")
    
    for scenario_key, scenario_name in scenarios.items():
        print(f"\n📋 正在生成 {scenario_name} 测试用例...")
        
        try:
            test_cases = generator.generate_complex_scenario_tests(scenario_key)
            
            # 保存测试用例
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{scenario_name}_测试用例_{timestamp}.txt"
            filepath = os.path.join("test_cases", filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(test_cases)
            
            print(f"✅ {scenario_name} 测试用例已生成: {filepath}")
            
            # 导出到Excel
            excel_filename = f"{scenario_name}_测试用例_{timestamp}.xlsx"
            excel_path = generator.export_to_excel(test_cases, excel_filename)
            print(f"📊 Excel文件已生成: {excel_path}")
            
        except Exception as e:
            print(f"❌ {scenario_name} 生成失败: {str(e)}")
    
    # 生成测试报告
    print(f"\n📄 生成测试报告...")
    report = generator.generate_test_report("全场景")
    report_filename = f"测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    report_path = os.path.join("test_cases", report_filename)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 测试报告已生成: {report_path}")
    
    print("\n🎉 所有测试用例生成完成！")
    print("💡 建议：")
    print("   1. 查看生成的测试用例文件")
    print("   2. 导入XMind进行可视化管理")
    print("   3. 根据项目进度调整测试优先级")
    print("   4. 定期更新测试用例覆盖范围")


if __name__ == "__main__":
    main()
