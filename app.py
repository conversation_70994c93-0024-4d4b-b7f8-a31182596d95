"""
跨境电商测试用例管理系统 - Flask Web应用
十年测试专家打造的专业测试用例管理平台
"""
from flask import Flask, render_template, request, jsonify, send_file
import json
import os
import sys
from datetime import datetime

# 添加scripts目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts'))

from test_case_engine import TestCaseEngine

app = Flask(__name__)
app.secret_key = 'test_case_management_system_2024'

# 全局变量
test_engine = None

def init_test_engine():
    """初始化测试引擎"""
    global test_engine
    try:
        test_engine = TestCaseEngine()
        return True
    except Exception as e:
        print(f"测试引擎初始化失败：{e}")
        return False

@app.route('/')
def index():
    """首页 - 显示测试用例分层结构"""
    if not test_engine:
        if not init_test_engine():
            return render_template('error.html', error="测试引擎初始化失败，请检查配置")
    
    structure = test_engine.get_test_structure()
    return render_template('index.html', structure=structure)

@app.route('/module/<path:module_path>')
def module_detail(module_path):
    """模块详情页 - 显示子模块或测试场景"""
    if not test_engine:
        return jsonify({'error': '测试引擎未初始化'})
    
    # 解析模块路径
    path_parts = module_path.split('/')
    scenarios = test_engine.get_module_scenarios(path_parts)
    
    return render_template('module_detail.html', 
                         module_path=module_path,
                         path_parts=path_parts,
                         scenarios=scenarios)

@app.route('/scenario/<scenario_name>')
def scenario_detail(scenario_name):
    """场景详情页 - 显示具体测试用例"""
    if not test_engine:
        return jsonify({'error': '测试引擎未初始化'})
    
    # 获取模板测试用例
    test_cases = test_engine.get_test_cases_for_scenario(scenario_name)
    
    return render_template('scenario_detail.html',
                         scenario_name=scenario_name,
                         test_cases=test_cases)

@app.route('/generate', methods=['POST'])
def generate_test_cases():
    """生成测试用例API"""
    if not test_engine:
        return jsonify({'error': '测试引擎未初始化'})
    
    data = request.get_json()
    scenario_name = data.get('scenario_name', '')
    custom_requirements = data.get('custom_requirements', '')
    
    if not scenario_name:
        return jsonify({'error': '场景名称不能为空'})
    
    try:
        # 生成测试用例
        test_cases = test_engine.generate_test_cases_by_scenario(scenario_name, custom_requirements)
        
        # 保存测试用例
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{scenario_name}_测试用例_{timestamp}.txt"
        filepath = test_engine.save_test_cases(test_cases, filename)
        
        return jsonify({
            'success': True,
            'test_cases': test_cases,
            'filepath': filepath,
            'filename': filename
        })
    except Exception as e:
        return jsonify({'error': f'生成失败：{str(e)}'})

@app.route('/api/structure')
def api_structure():
    """获取测试结构API"""
    if not test_engine:
        return jsonify({'error': '测试引擎未初始化'})
    
    structure = test_engine.get_test_structure()
    return jsonify(structure)

@app.route('/api/scenarios/<path:module_path>')
def api_scenarios(module_path):
    """获取模块场景API"""
    if not test_engine:
        return jsonify({'error': '测试引擎未初始化'})
    
    path_parts = module_path.split('/')
    scenarios = test_engine.get_module_scenarios(path_parts)
    return jsonify(scenarios)

@app.route('/download/<filename>')
def download_file(filename):
    """下载测试用例文件"""
    filepath = os.path.join('test_cases', filename)
    if os.path.exists(filepath):
        return send_file(filepath, as_attachment=True)
    else:
        return "文件不存在", 404

@app.route('/files')
def list_files():
    """列出所有测试用例文件"""
    files = []
    test_cases_dir = 'test_cases'
    
    if os.path.exists(test_cases_dir):
        for filename in os.listdir(test_cases_dir):
            if filename.endswith(('.txt', '.xlsx', '.csv', '.json')):
                filepath = os.path.join(test_cases_dir, filename)
                stat = os.stat(filepath)
                files.append({
                    'name': filename,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
    
    # 按修改时间倒序排列
    files.sort(key=lambda x: x['modified'], reverse=True)
    
    return render_template('files.html', files=files)

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('error.html', error="页面不存在"), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return render_template('error.html', error="服务器内部错误"), 500

if __name__ == '__main__':
    print("🔦 跨境电商测试用例管理系统")
    print("=" * 50)
    print("🚀 正在启动Flask Web应用...")
    
    # 初始化测试引擎
    if init_test_engine():
        print("✅ 测试引擎初始化成功")
        print("🌐 Web界面地址：http://localhost:5000")
        print("📋 功能特色：分层测试用例展示、AI智能生成、专业测试管理")
        print("=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ 测试引擎初始化失败，请检查配置")
        print("💡 请确保已配置ERNIE_ACCESS_TOKEN环境变量")
